FROM node:16-alpine

WORKDIR /app

RUN apk add --no-cache \
    git \
    bash \
    curl \
    openssh-client \
    docker-cli \
    sudo \
    python3 \
    py3-pip \
    iproute2

RUN mkdir -p /usr/local/lib/docker/cli-plugins && \
    curl -SL https://github.com/docker/compose/releases/download/v2.17.2/docker-compose-linux-x86_64 -o /usr/local/lib/docker/cli-plugins/docker-compose && \
    chmod +x /usr/local/lib/docker/cli-plugins/docker-compose

RUN docker compose version


COPY . .

RUN npm install
RUN npm link

ENTRYPOINT ["pacto"]