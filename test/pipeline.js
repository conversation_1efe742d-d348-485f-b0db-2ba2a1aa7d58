const fs = require('fs');
const { exec, execSync } = require('child_process');
const path = require('path');
const axios = require('axios');
const inquirer = require('inquirer');
const unzipper = require('unzipper');
const url = require('url');
const { log } = require('../util/log.js');
const { root } = require('../util/path.js');
const { getGitlabToken } = require('../git/gitlab.js');
const { setValue, getValue } = require('../util/storage.js');
let open;
(async () => {
    open = (await import('open')).default;
})();

const projectId = 33594913;

async function fetchAndConnect() {
    const gitlabToken = await getGitlabToken();

    axios.get(`https://gitlab.com/api/v4/projects/${projectId}/pipelines`, {
        headers: {
            'PRIVATE-TOKEN': gitlabToken
        }
    })
        .then((response) => {
            const pipelines = response.data;
            const latestPipelines = pipelines.slice(0, 5);
            const choices = latestPipelines.map((pipeline) => {
                const executedAt = new Date(pipeline.created_at).toLocaleString('pt-BR');

                return {
                    name: `Pipeline ${pipeline.id} - ${pipeline.status} - Executada em: ${executedAt}`,
                    value: pipeline.id
                };
            });

            return inquirer.prompt([
                {
                    type: 'list',
                    name: 'pipelineId',
                    message: 'Por favor, selecione um ID de pipeline:',
                    choices: choices
                },
            ]);
        })
        .then(async (respostas) => {
            await connect(respostas.pipelineId);
        });
}

async function getPipelineVariables(pipelineId) {
    const value = getValue(`pipeline_variables_${pipelineId}`);
    if(value && !(Array.isArray(value) && value.length === 0)){
        return value;
    }
    const privateToken = await getGitlabToken();
    const url = `https://gitlab.com/api/v4/projects/${projectId}/pipelines/${pipelineId}/variables`;

    try {
        const response = await axios.get(url, {
            headers: {
                'PRIVATE-TOKEN': privateToken
            }
        });
        setValue(`pipeline_variables_${pipelineId}`, response.data)
        return response.data;
    } catch (error) {
        
        if(error.status === 403){
            console.error(`Seu usuário ou personal token não tem acesso a consultar dados de pipelines iniciadas por outro usuário. Informe ao Lider Técnico do time de QA para alterar suas permissões no repositório de teste`);
        }else{
            console.error('Erro ao buscar variáveis de ambiente:', error);
        }

        process.exit(1);
    }
}

async function getPipelineVariable(pipelineId, variableName){
    const variables = await getPipelineVariables(pipelineId);
    const variable = variables.find(variable => variable.key === variableName);

    if(!variable){
        throw new Error(`Variável ${variableName} não encontrada na pipeline ${pipelineId}. Aguarde a pipline fianlizar o stage prepare (deploy) para conectar`);
    }

    return variable.value;
}

async function isTestEnvironmentProd(pipelineId){
    const testEnvironment = await getPipelineVariable(pipelineId, 'TEST_ENVIRONMENT');
    return testEnvironment === 'prod';
}

async function isTestEnvironmentStaging(pipelineId){
    const testEnvironment = await getPipelineVariable(pipelineId, 'TEST_ENVIRONMENT');
    return testEnvironment === 'staging';
}

async function getPipelineJobDeployName(pipelineId){
    const isProd = await isTestEnvironmentProd(pipelineId);
    if(isProd){
        return 'deploy-app-prod';
    }

    const isStaging = await isTestEnvironmentStaging(pipelineId);
    if(isStaging){
        return 'deploy-app-staging';
    }

    throw new Error('Não foi possível identificar o ambiente de teste.');
}

async function connect(pipelineId) {
    console.info('Configuração do ambiente PIPE finalizada.');
    open(`http://${pipelineId}-zw.pactoteste.com/ZillyonWeb`);
}

function listAndSelectUrl(envParsed) {
    const choices = Object.keys(envParsed)
        .filter(key => key.startsWith('URL_'))
        .map(key => ({
            name: `${key}: ${envParsed[key]}`,
            value: envParsed[key]
        }));

    inquirer.registerPrompt('autocomplete', require('inquirer-autocomplete-prompt'));

    return inquirer.prompt([
        {
            type: 'autocomplete',
            name: 'selectedUrl',
            message: 'Selecione a URL que você deseja abrir:',
            source: function (answersSoFar, input) {
                input = input || '';
                return new Promise(function (resolve) {
                    const filtered = choices.filter(choice => choice.name.toLowerCase().includes(input.toLowerCase()));
                    resolve(filtered);
                });
            }
        }
    ]);
}

function openSelectedUrl(envParsed) {
    listAndSelectUrl(envParsed).then(function (answers) {
        open(answers.selectedUrl);
    }).catch(function (error) {
        console.error('Erro ao selecionar a URL:', error);
    });
}

function runCommandConfigHost(domains, ip) {
    const domainsParam = domains ? `--domains ${domains.join(',')}` : '';
    const ipParam = ip ? `--ip ${ip}` : '';
    const indexFilePath = path.resolve(root(), `index.js`);
    const cmd = `DEBUG=${process.env.DEBUG} node ${indexFilePath} network hosts-pipe ${domainsParam} ${ipParam}`;
    log('pacto:network', 'Executando comando', cmd);
    execSync(cmd);
}

function extraDomainsFromEnv(envs) {
    const keys = Object.keys(envs);

    const urlKeys = keys.filter(key => key.startsWith('URL_') && !(process.env['DEPLOY_ENVIRONMENT'] === 'local' && key === 'URL_ARAGORN'));

    const domains = [...new Set(urlKeys.map(key => new url.URL(envs[key]).hostname)
        .filter(domain => domain !== null && domain !== undefined && domain !== ''))];

    return domains;
}

function configHosts(domains) {

    addDomainsToHost(domains, ip.serverPipe);

    if (getOperatingSystem() === 'macOS') {
        exec('sudo killall -HUP mDNSResponder', (error, stdout, stderr) => {
            if (error) {
                console.error(`Erro ao executar o comando: ${error}`);
                return;
            }
            if (stderr) {
                console.error(`Erro: ${stderr}`);
                return;
            }
            console.log(`Resultado: ${stdout}`);
        });
    }
}

async function downloadArtifacts(pipelineId, jobName) {
    const jobId = await getJobId(pipelineId, jobName);

    return new Promise(async (resolve, reject) => {
        if (jobId) {
            const token = await getGitlabToken();
            const url = `https://gitlab.com/api/v4/projects/${projectId}/jobs/${jobId}/artifacts`;

            axios({
                url,
                method: 'GET',
                responseType: 'stream',
                headers: {
                    'PRIVATE-TOKEN': token
                }
            })
                .then((response) => {
                    const dirArtfactsPath = path.join(root(), '.tmp', 'artifacts', pipelineId.toString());
                    const dirExtractPath = path.join(root(), '.tmp', 'extract', pipelineId.toString());

                    if (!fs.existsSync(dirArtfactsPath)) {
                        fs.mkdirSync(dirArtfactsPath, { recursive: true });
                    }

                    if (!fs.existsSync(dirExtractPath)) {
                        fs.mkdirSync(dirExtractPath, { recursive: true });
                    }

                    const zipPath = path.resolve(dirArtfactsPath, `artifacts-${pipelineId}-${jobId}.zip`);
                    const writer = fs.createWriteStream(zipPath);

                    response.data.pipe(writer);

                    writer.on('finish', async () => {

                        try {
                            fs.createReadStream(zipPath)
                                .pipe(unzipper.Extract({ path: dirExtractPath }))
                                .on('close', () => {
                                    resolve(dirExtractPath);
                                })
                                .on('error', reject);
                        } catch (error) {
                            const msg = `Erro ao descompactar os arquivos de artefatos do job "${jobName}" (ID: ${jobId}) da pipeline ${pipelineId}:`;
                            reject(msg, error);
                        }
                    });

                    writer.on('error', (error) => {
                        const msg = `Erro ao baixar os artefatos do job "${jobName}" (ID: ${jobId}) da pipeline ${pipelineId}:`;
                        console.error(msg, error);
                        reject(msg, error);
                    });
                })
                .catch((error) => {
                    const msg = `Erro ao baixar os artefatos do job "${jobName}" (ID: ${jobId}) da pipeline ${pipelineId}:`;
                    console.error(msg, error);
                    reject(msg, error);
                });
        } else {
            const msg = `Não foi possível baixar os artefatos do job "${jobName}" da pipeline ${pipelineId}.`;
            reject(msg);
        }
    });
}

async function getJobId(pipelineId, jobName) {
    const url = `https://gitlab.com/api/v4/projects/${projectId}/pipelines/${pipelineId}/jobs`;
    const token = await getGitlabToken();

    const response = await axios.get(url, {
        headers: {
            'PRIVATE-TOKEN': token
        }
    });

    const job = response.data.find(job => job.name === jobName);

    if (job) {
        return job.id;
    } else {
        throw new Error(`Job "${jobName}" não encontrado na pipeline ${pipelineId}.`);
    }
}

async function runPipeline(gitlabToken) {
    const options = {
        headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer ' + gitlabToken,
        },
    };

    inquirer
        .prompt([
            {
                type: 'list',
                name: 'environment',
                message: 'Em qual ambiente você deseja executar a pipeline?',
                default: getValue('test:run:environment') || 'PROD',
                choices: [
                    { name: 'Cluster da pipeline oficial de testes', value: 'PROD' }, 
                    { name: 'Zona 74 - Ambiente de produção', value: 'ZW74' }, 
                    { name: 'Zona 70 - Ambiente de produção', value: 'ZW70' },
                    { name: 'Todas as zonas - Ambiente de produção', value: 'ZWALL' },
                    // Não é mais utilizado
                    // { name: 'Cluster de desenvolvimento de testes', value: 'staging' }
                ],
            },
            {
                type: 'confirm',
                name: 'buildCypress',
                message: 'Você precisa buildar o container do Cypress?',
                default: getValue('test:run:buildCypress') || true,
            },
            {
                type: 'confirm',
                name: 'cleanup',
                message: 'Você deseja executar a limpeza no ambinete?',
                default: getValue('test:run:cleanup') || true,
            },
            {
                type: 'text',
                name: 'branch',
                default: getValue('test:run:branch') || 'master',
                message: 'Qual branch do teste você quer executar a pipeline?',
            },
            {
                type: 'text',
                name: 'appBranchs',
                default: getValue('test:run:appBranchs') || '',
                message: 'Quais branchs das aplicações você quer executar a pipeline?',
            },
            { 
                type: 'text',
                name: 'cypressSpec',
                default: getValue('test:run:cypressSpec') || '',
                message: 'Escolha quais testes executar, especificando o caminho do teste separado por virgula'
            },
            {
                type: 'text',
                name: 'cypressBurn',
                default: getValue('test:run:cypressBurn') || 1,
                message: 'Você deseja repetir os testes quantas vezes?'
            },
            {
                tyep: 'text',
                name: 'cypressCommitMessage',
                default: getValue('test:run:cypressBurn') || '',
                message: 'Qual é o objetivo desta pipeline?'
            }
        ])
        .then((answers) => {
            setValue('test:run:environment', answers.environment);
            const variables = getVariablesByEnvironemnt(answers.environment);


            setValue('test:run:buildCypress', answers.buildCypress)
            if (answers.buildCypress) {
                variables.push({ key: 'BUILD_CYPRESS', value: 'true' });
            } else {
                variables.push({ key: 'BUILD_CYPRESS', value: 'false' });
            }


            setValue('test:run:cleanup', answers.cleanup);
            if (answers.cleanup) {
                variables.push({ key: 'CLEANUP', value: 'true' });
            } else {
                variables.push({ key: 'CLEANUP', value: 'false' });
            }

            setValue('test:run:appBranchs', answers.appBranchs);
            if(answers.appBranchs){
               variables.push({ key: 'APP_BRANCHS', value: answers.appBranchs.replace(/\s+/g, '') });
            }

            setValue('test:run:cypressSpec', answers.cypressSpec);
            if(answers.cypressSpec){
                variables.push({key: 'CYPRESS_SPEC', value: answers.cypressSpec.replace(/\s+/g, '')});
            }

            setValue('test:run:cypressBurn', answers.cypressBurn);
            if(answers.cypressBurn && answers.cypressBurn > 1){
                variables.push({key: 'CYPRESS_BURN', value: answers.cypressBurn});
            }

            setValue('test:run:cypressCommitMessage', answers.cypressCommitMessage);
            if(answers.cypressCommitMessage){
                variables.push({key: 'CYPRESS_COMMIT_MESSAGE', value: answers.cypressCommitMessage})
            }

            const url = `https://gitlab.com/api/v4/projects/${projectId}/pipeline`;
            axios
                .post(url,
                    {
                        ref: answers.branch,
                        variables: variables
                    },
                    options)
                .then((res) => {
                    console.log('Pipeline iniciada: ', res.data.web_url);
                    console.log('Vou abrir o link para você ;)');
                    open(res.data.web_url);
                })
                .catch((error) => {
                    if(error.response.data.message.base.includes('Reference not found')){
                        console.error(`Branch ${answers.branch} não existe no repositório teste-auto. Verifique se a branch informada existe no repositório.`);
                        process.exit(1);
                    }
                    console.error(error.message)
                });
        });
}


function getVariablesByEnvironemnt(environment) {
    const environments = {
        PROD: [
            { key: 'TEST_ENVIRONMENT', value: 'prod' },
            { key: 'CYPRESS_REPLICAS', value: '35' }
        ],
        ZW74: [
            { key: 'TEST_ENVIRONMENT', value: 'ZW74' },
            { key: 'CYPRESS_REPLICAS', value: '1' },
            {
                key: 'CYPRESS_SPEC',
                value: 'cypress/integration/1-TelaPrincipal/0-AcessoModulos/AcessoModulos&Fucionalidades.spec.js'
            },
        ],
        ZWALL: [
            { key: 'TEST_ENVIRONMENT', value: 'ZWALL' },
            { key: 'CYPRESS_REPLICAS', value: '1' },
            {
                key: 'CYPRESS_SPEC',
                value: 'cypress/integration/1-TelaPrincipal/0-AcessoModulos/AcessoModulos&Fucionalidades.spec.js'
            },
        ],
        staging: [
            { key: 'TEST_ENVIRONMENT', value: 'staging' },
            { key: 'CYPRESS_REPLICAS', value: '4' }
        ],
    }

    return environments[environment];
}

module.exports = { fetchAndConnect, connect, runCommandConfigHost, extraDomainsFromEnv, configHosts, downloadArtifacts, getJobId, runPipeline };
