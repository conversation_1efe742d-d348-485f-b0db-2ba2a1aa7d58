const { execSync } = require('child_process');
const axios = require('axios');


async function waitTest(stack_number, timeout, timeout_no_search_service, interval, notificate_google_chat) {
    let service_name = `test-${stack_number}_cypress`;
    let time = 0;
    let time_no_search_service = 0;
    let timeoutExceded = false;

    while (true) {
        try { 
            if((timeout !== 0 && time >= timeout)) {
                timeoutExceded = true;
                const msg = `O timeout do teste foi excedido em ${timeout}ms.`;
                console.log(msg);
                const resultTimeout = execSync(`docker service ps ${service_name}`).toString();
                console.error(resultTimeout);
                if(notificate_google_chat) {
                    await sendNotificationToGoogleChat(msg);
                }
                process.exit(1);
            }

            if(time_no_search_service >= timeout_no_search_service) {
                timeoutExceded = true;
                const msg = `O timeout de serviço não encontrado foi excedido em ${timeout_no_search_service}ms.`;
                console.log(msg);
                if(notificate_google_chat) {
                    await sendNotificationToGoogleChat(msg);
                }
                process.exit(1);
            }

            let tasksStatus = execSync(`docker service ps ${service_name} --format '{{.Name}}: {{.CurrentState}}'`).toString();
            tasksStatus = tasksStatus.split('\n');
            const tasksRunning = tasksStatus.filter(task => task.includes('Running') || task.includes('Starting') || task.includes('Preparing'));
            const tasksFailed = tasksStatus.filter(task => task.includes('Failed') || task.includes('Reject'));
    
            if(tasksRunning.length > 0) {
                console.log(`Tarefas de teste do service ${service_name} estão executando.`);
                if(process.env.DEBUG_DEPLOY === 'true') {
                    const resultRunning = execSync(`docker service ps ${service_name}`).toString();
                    console.info(resultRunning);
                }
                
                await sleep();
                continue;
            }
    
            if(tasksFailed.length > 0){
                const msg = `Alguns testes do service ${service_name} falharam.`;
                console.log(msg);
                const resultFailed = execSync(`docker service ps ${service_name}`).toString();
                console.error(resultFailed);
                if(notificate_google_chat) {
                    await sendNotificationToGoogleChat(msg);
                }
                process.exit(1);
            }
    
            if(tasksFailed.length === 0) {
                const msg = `Todas os testes do service ${service_name} foram executados com sucesso.`;
                console.log(msg);
                const resultSuccess = execSync(`docker service ps ${service_name}`).toString();
                console.info(resultSuccess);
                if(notificate_google_chat) {
                    await sendNotificationToGoogleChat(msg);
                }
                process.exit(0);
            }
        } catch (error) {
            if(timeoutExceded) {
                process.exit(1);
            }

            if(error && error.message && error.message.toLowerCase().includes('no such service')) {
                console.log(`O Service ${service_name} não foi encontrado. Aguardando...`);
                time_no_search_service += interval;
                await sleep(interval);
                continue;
            }
    
            const msg = `Erro ao aguardar o teste: ${error}`;
            console.error('Erro ao aguardar o teste: ', error);
            if(notificate_google_chat) {
                await sendNotificationToGoogleChat(msg);
            }
            process.exit(1);
        }
    }
}

async function sleep(interval, time) {
    time += interval;
    return new Promise(resolve => setTimeout(resolve, interval));
}

async function sendNotificationToGoogleChat(msg) {
    const webhookUrl = 'https://chat.googleapis.com/v1/spaces/AAAA9CQXzfA/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=m-Gf70kxX-dzNGOtJUwr57V0N5kqEpbrVDCNG9CswYw';
    const payload = {
        text: msg
    };

    const config = {
        headers: {
            'Content-Type': 'application/json; charset=UTF-8'
        }
    };
    try {
        const response = await axios.post(webhookUrl, payload, config);
        console.log('Enviado a notificação para google chat:', response);
    } catch (error) {
        console.error('Erro ao enviar a notificação para google chat:', error.response.data.error);
    }
}

module.exports = { waitTest };