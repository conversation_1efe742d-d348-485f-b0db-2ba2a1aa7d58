const inquirer = require('inquirer');
const { fetchAndConnect } = require('./pipeline.js');
const { waitTest } = require('./wait.js');
const { getGitlabToken } = require('../git/gitlab.js');
const { runPipeline } = require('./pipeline.js');
let open;
(async () => {
  open = (await import('open')).default;
})();

function addTestCommands(program) {
    const test = program
        .command('test')
        .alias('t')
        .description('Executa pipelines de teste para liberacão de versão');

    test.command('run-pipeline')
        .alias('r')
        .description('Executa a pipeline de teste')
        .action(async () => {
            const gitlabToken = await getGitlabToken();
            await runPipeline(gitlabToken);
        });

    test.command('connect')
        .alias('c')
        .description('Conecta a um ambiente criado na pipeline de testes')
        .action(async () => {
            try{
                await fetchAndConnect();
            }catch(e){
                console.error(e);
                process.exit(1);
            }
        });

    test.command('wait')
        .alias('w')
        .description('Aguarda a execução dos serviços de teste finalizar')
        .option('-s, --stack_number <stack_number>', 'Número do stack de teste')
        .option('-t, --timeout <timeout>', 'Timeout para finalização dos testes')
        .option('-tnss, --timeout_no_search_service <timeout_no_search_service>', 'Timeout para finalização dos testes')
        .option('-i, --interval <interval>', 'Intervalo de verificação dos testes')
        .option('-ngc, --notificate_google_chat <notificate_google_chat>', 'Notificar no Google Chat')
        .action(async (cmd) => {
            const parameters = await getWaitParameters(
                cmd.stack_number, 
                cmd.action.timeout, 
                cmd.action.timeout_no_search_service, 
                cmd.action.interval, 
                cmd.action.notificate_google_chat);

            const { stack_number, timeout, timeout_no_search_service, interval, notificate_google_chat } = parameters;
            await waitTest(stack_number, timeout, timeout_no_search_service, interval, notificate_google_chat);
        });
}


async function getWaitParameters(stack_number, timeout, timeout_no_search_service, interval, notificate_google_chat) {
    const questions = [
        {
            type: 'input',
            name: 'stack_number',
            message: 'Stack number:',
            default: stack_number
        },
        {
            type: 'input',
            name: 'timeout',
            message: 'Timeout:',
            default: timeout
        },
        {
            type: 'input',
            name: 'timeout_no_search_service',
            message: 'Timeout no search service:',
            default: timeout_no_search_service
        },
        {
            type: 'input',
            name: 'interval',
            message: 'Interval:',
            default: interval
        },
        {
            type: 'confirm',
            name: 'notificate_google_chat',
            message: 'Notificate Google Chat:',
            default: notificate_google_chat
        }
    ];

    const answers = await inquirer.prompt(questions.filter(q => !q.default));
    service_name = `test-${answers.stack_number}_cypress`;
    notificateGoogleChat = answers.notificate_google_chat;

    return answers;
}

module.exports = { addTestCommands };