const axios = require('axios');
const { getGitlabToken, getProjectIdFromRepoName } = require('../git/gitlab.js');

async function fetchReadme() {
    const gitlabToken = await getGitlabToken();
    const projectId = await getProjectIdFromRepoName('teste-auto');
    const url = `https://gitlab.com/api/v4/projects/${projectId}/repository/files/README.md/raw?ref=master`;

    try {
        const response = await axios.get(url, {
            headers: {
                'PRIVATE-TOKEN': gitlabToken
            }
        });
        return response.data;
    } catch (error) {
        console.error(`Failed to fetch README: ${error.message}`);
        throw error;
    }
}

function extractDockerTags(readmeContent) {
    const regex = /\|\s*\*\*(.*?)\*\*\s*\|\s*(.*?)\s*\|/g;
    const dockerTagsArray = [];
    let match;

    while ((match = regex.exec(readmeContent)) !== null) {
        const release = match[1].trim();
        const dockerTags = match[2].split(',').map(tag => tag.trim());
        dockerTagsArray.push({ release, dockerTags });
    }

    const dockerTagsArrayWithoutHeader = dockerTagsArray.slice(1);

    return dockerTagsArrayWithoutHeader;
}

async function getDockerTags() {
    try {
        const readmeContent = await fetchReadme();
        const dockerTags = extractDockerTags(readmeContent, 'teste-auto');
        return dockerTags;
    } catch (error) {
        console.error(`Error fetching Docker tags: ${error.message}`);
        throw error;
    }
}

module.exports = { getDockerTags, fetchReadme };