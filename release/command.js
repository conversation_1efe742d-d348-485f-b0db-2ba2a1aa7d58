const { getDockerTags } = require('./releases.js');

function addReleaseCommand(program) {
    const d = program
        .command('release')
        .alias('r')
        .description('Realiza consultas de informações das releases dos sistemas pacto');

    d.command('release-tags')
        .alias('t')
        .description('Consulta as tags de Docker de um sistema Pacto a partir do README do projeto de teste aonde são documentadas as tags de cada versão.')
        .action(async () => {
            try {
                const dockerTags = await getDockerTags();
                console.log('Docker Tags:', dockerTags);
            } catch (error) {
                console.error(`Error fetching Docker tags: ${error.message}`);
            }
        });
}

module.exports = { addReleaseCommand };
