const { runDatabaseCommand, listDatabases, migrateDbZw, migrateDbTreino } = require('./database.js');
const { findZwContainer, findTreinoContainer, getContainerEnvVariable } = require('./../deploy/container.js');
const ora = require("ora");
const emoji = require("node-emoji");

function addDatabaseCommand(program) {
    const d = program
        .command('database')
        .alias('db')
        .description('Comandos relacionados ao banco de dados dos sistemas pacto');

    d.command('restore [chaveOuNomeEmpresa]')
        .alias('r')
        .description('Restaura o banco de dados de uma empresa')
        .action(async (chaveOuNomeEmpresa) => {
            await runDatabaseCommand(chaveOuNomeEmpresa);
        });

    d.command('list')
            .alias('l')
            .description('Lista os bancos de dados disponíveis')
            .action(async () => {
                const empresas = await listDatabases();
                console.log('Empresas disponíveis:');
                empresas.forEach(empresa => {
                    console.log(`${empresa.chave} | ${empresa.identificadorempresa}`);
                });
            });

    d.command('migrate [chave]')
            .alias('m')
            .description('Migra o banco de dados para a versão mais recente')
            .action(async (chave) => {
                if (!chave) {
                    chave = 'teste';
                    console.log(`Nenhuma chave especificada, usando chave padrão: ${chave}`);
                }
                
                const spinner = ora(
                    `Iniciando migração do banco de dados para a chave: ${chave} ${emoji.get("hourglass_flowing_sand")}`
                ).start();
                
                try {
                    // Migrar ZW
                    const containerZwName = await findZwContainer();
                    if (containerZwName) {
                        const urlZw = await getContainerEnvVariable(containerZwName, "URL_ZW");
                        spinner.text = `Executando migrate do banco do ZW ${emoji.get("hourglass_flowing_sand")}`;
                        await migrateDbZw(urlZw, chave);
                        spinner.succeed(`Migração do banco ZW concluída com sucesso! ${emoji.get("white_check_mark")}`);
                    } else {
                        spinner.info(`Container ZW não encontrado, pulando migração do ZW.`);
                    }
                    
                    // Migrar Treino
                    const containerTreinoName = await findTreinoContainer();
                    if (containerTreinoName) {
                        const urlTreino = await getContainerEnvVariable(containerTreinoName, "URL_TREINO");
                        spinner.text = `Executando migrate do banco do Treino ${emoji.get("hourglass_flowing_sand")}`;
                        await migrateDbTreino(urlTreino, chave);
                        spinner.succeed(`Migração do banco Treino concluída com sucesso! ${emoji.get("white_check_mark")}`);
                    } else {
                        spinner.info(`Container Treino não encontrado, pulando migração do Treino.`);
                    }
                    
                    spinner.succeed(`Migração de todos os bancos de dados concluída! ${emoji.get("rocket")}`);
                } catch (error) {
                    spinner.fail(`Erro ao migrar o banco de dados: ${error.message} ${emoji.get("x")}`);
                    console.error(error);
                    process.exit(1);
                }
            });
    
}

module.exports = { addDatabaseCommand };