const axios = require("axios");
const inquirer = require("inquirer");
inquirer.registerPrompt(
  "autocomplete",
  require("inquirer-autocomplete-prompt"),
);
const { exec } = require("child_process");
const { getValue, setValue } = require("../util/storage");
const ora = require("ora");
const emoji = require("node-emoji");
const waitOn = require("wait-on");
const open = require("open");
const { getContainerEnvVariable } = require("../deploy/container");

async function searchCompanies(chaveOuNomeEmpresa) {
  const url = `http://app.pactosolucoes.com.br/oamd/prest/empresa/pesquisarEmpresas?chaveOuNomeEmpresa=${chaveOuNomeEmpresa}`;
  try {
    const response = await axios.get(url);
    return response.data.return;
  } catch (error) {
    console.error(`Erro ao pesquisar empresas: ${error.message}`);
    throw error;
  }
}

function isDatabaseDownloaded(chave) {
  return getValue("db:downloaded:" + chave);
}

async function findPostgresContainer() {
  return new Promise((resolve, reject) => {
    exec("docker ps", (err, stdout, stderr) => {
      if (err) {
        return reject(`Erro ao executar o comando Docker: ${stderr}`);
      }
  
      const lines = stdout.split("\n").slice(1);
  
      const postgresContainer = lines.find((line) =>
        line.includes("registry.gitlab.com/plataformazw/docker-pacto/postgres:")
      );
  
      if (postgresContainer) {
        const containerName = postgresContainer.split(" ")[0];
        resolve(containerName);
      } else {
        reject("Nenhum container Postgres encontrado");
      }
    });
  });
}

async function findZwContainer() {
  return new Promise((resolve, reject) => {
    // Executa o comando 'docker ps' para listar os containers sem o 'format'
    exec("docker ps", (err, stdout, stderr) => {
      if (err) {
        return reject(`Erro ao executar o comando Docker: ${stderr}`);
      }

      // Split the output into lines (skipping the first header line)
      const lines = stdout.split("\n").slice(1); // Skips the first header line

      // Loop through the lines to find the container with the desired image
      const zwContainer = lines.find((line) =>
        line.includes("registry.gitlab.com/plataformazw/zw/tomcat:")
      );

      if (zwContainer) {
        // Extract the container name (the first word of the line)
        const containerName = zwContainer.split(" ")[0];
        resolve(containerName);
      } else {
        resolve(null); // Nenhum container encontrado
      }
    });
  });
}


async function restartContainer(containerName) {
  return new Promise((resolve, reject) => {
    exec(`docker restart ${containerName}`, (err, stdout, stderr) => {
      if (err) {
        console.error(
          `Erro ao reiniciar o container ${containerName}: ${err.message}`,
        );
        reject(err);
        return;
      }
      const containerName = stdout.trim();
      if (!containerName) {
        reject(
          new Error(`Nenhum container encontrado com nome ${containerName}.`),
        );
        return;
      }
      resolve(containerName);
    });
  });
}

async function restoreDatabase(chave, baixar, saveAs) {
    await new Promise(async (resolve, reject) => {
      try {
        const containerName = await findPostgresContainer();
        exec(
          `docker exec ${containerName} restore-all.sh ${chave} ${baixar} ${saveAs}`,
          (err, stdout, stderr) => {
            if (err && err.killed) {
              reject(err);
              return;
            }
            resolve();
          },
        );
      }catch (error) {
        reject(error);
      }
    });
}

async function healthCheck(url) {
  let resource = url
    .replace("http://", "http-get://")
    .replace("https://", "https-get://");

  const opts = {
    resources: [resource],
    interval: 5000,
    timeout: 360000,
    verbose: false,
  };

  await waitOn(opts);
}

async function migrateDbZw(baseUrlZw, chave) {
  chave = chave === undefined ? 'teste' : chave;
  try {
    await axios.post(`${baseUrlZw}/prest/versao?chave=${chave}`);
  } catch (error) {
    if (
      error &&
      error.response &&
      error.response.data &&
      error.response.data.mensagem &&
      (error.response.data.mensagem.includes(
        "A versão corrente do Banco de Dados do sistema já a especificada",
      ) ||
        error.response.data.mensagem.includes(
          "A vers�o corrente do Banco de Dados do sistema j� � a especificada",
        ))
    ) {
      return;
    }
    console.error(
      "Erro ao migrar base de dados do zw:",
      error.code,
      error.message,
      error.response.data,
    );
    process.exit(1);
  }
}


async function findDiscoveryContainer() {
  return new Promise((resolve, reject) => {
    exec("docker ps", (err, stdout, stderr) => {
      if (err) {
        return reject(`Erro ao executar o comando Docker: ${stderr}`);
      }

      const lines = stdout.split("\n").slice(1); 
      
      for (const line of lines) {
        const columns = line.split(/\s+/);
        const imageName = columns[1]; 
        
        if (imageName && imageName.startsWith("registry.gitlab.com/plataformazw/discovery-urls:")) {
          resolve(columns[0]);
          return;
        }
      }

      resolve(null);
    });
  });
}


async function findLoginContainer() {
  return new Promise((resolve, reject) => {
    exec("docker ps", (err, stdout, stderr) => {
      if (err) {
        return reject(`Erro ao executar o comando Docker: ${stderr}`);
      }

      const lines = stdout.split("\n").slice(1); // Remove o cabeçalho
      
      for (const line of lines) {
        const columns = line.split(/\s+/); // Divide por espaços múltiplos
        const imageName = columns[1];
        
        if (imageName && imageName.startsWith("registry.gitlab.com/plataformazw/login/tomcat:")) {
          resolve(columns[0]);
          return;
        }
      }

      resolve(null);
    });
  });
}


async function findTreinoContainer() {
  return new Promise((resolve, reject) => {
    exec("docker ps", (err, stdout, stderr) => {
      if (err) {
        return reject(`Erro ao executar o comando Docker: ${stderr}`);
      }

      const lines = stdout.split("\n").slice(1); // Remove o cabeçalho
      
      for (const line of lines) {
        const columns = line.split(/\s+/); 
        const imageName = columns[1];
        
        if (imageName && imageName.startsWith("registry.gitlab.com/plataformazw/treino/tomcat:")) {
          resolve(columns[0]); 
          return;
        }
      }

      resolve(null);
    });
  });
}


async function migrateDbTreino(baseUrlTreino, chave) {
  try {
    await axios.post(`${baseUrlTreino}/prest/config/${chave}/updateBD`);
  } catch (error) {
    console.error(
      "Error on migrate treino database:",
      error.code,
      error.message,
      error.response.data,
    );
    process.exit(1);
  }
}

async function runDatabaseCommand(chaveOuNomeEmpresa) {
  let spinner = null;
  try {
    if (!chaveOuNomeEmpresa) {
      const answers = await inquirer.prompt([
        {
          type: "input",
          name: "chaveOuNomeEmpresa",
          message: "Informe a chave ou nome da empresa:",
          default: getValue("db:chaveOuNomeEmpresa"),
        },
      ]);
      chaveOuNomeEmpresa = answers.chaveOuNomeEmpresa;
    }

    setValue("db:chaveOuNomeEmpresa", chaveOuNomeEmpresa);

    let selectedCompany = null;
    if (chaveOuNomeEmpresa !== "teste") {
      const companies = await searchCompanies(chaveOuNomeEmpresa);
      if (companies.length === 0) {
        console.log(
          "Nenhuma empresa encontrada com a chave ou nome " +
            chaveOuNomeEmpresa,
        );
        process.exit(0);
      }

      if (companies.length === 1) {
        selectedCompany = companies[0].chave;
        console.log(`Empresa encontrada: ${companies[0].identificadorEmpresa}`);
      } else {
        selectedCompanyResponse = await inquirer.prompt([
          {
            type: "autocomplete",
            name: "selectedCompany",
            message: "Selecione a empresa:",
            source: (answersSoFar, input) => {
              input = input || "";
              return new Promise((resolve) => {
                const filteredCompanies = companies.filter((company) =>
                  company.identificadorEmpresa
                    .toLowerCase()
                    .includes(input.toLowerCase()),
                );
                resolve(
                  filteredCompanies.map((company) => ({
                    name: company.identificadorEmpresa + " - " + company.chave,
                    value: company.chave,
                  })),
                );
              });
            },
          },
        ]);
        selectedCompany = selectedCompanyResponse.selectedCompany;
      }
    } else {
      selectedCompany = "teste";
    }

    const saveAsDefault = getValue("db:saveAs:" + selectedCompany);
    const { saveAs } = await inquirer.prompt([
      {
        type: "input",
        name: "saveAs",
        message: "Informe o nome da chave que deseja salvar:",
        default: saveAsDefault || selectedCompany,
        validate: (input) => {
          const invalidCharacters = /[^a-zA-Z0-9_]/;
          if (invalidCharacters.test(input)) {
            return "O nome da chave não pode conter espaços ou caracteres especiais.";
          }
          return true;
        },
      },
    ]);
    setValue("db:saveAs:" + selectedCompany, saveAs);

    const chave = selectedCompany;
    let baixar = true;

    if (isDatabaseDownloaded(chave)) {
      let lastdownloadAgainOption = getValue("db:downloadAgain:" + chave);
      if (
        lastdownloadAgainOption === undefined ||
        lastdownloadAgainOption === null
      ) {
        lastdownloadAgainOption = false;
      }

      const { downloadAgain } = await inquirer.prompt([
        {
          type: "confirm",
          name: "downloadAgain",
          message: "O banco de dados já foi baixado. Deseja baixar novamente?",
          default: lastdownloadAgainOption,
        },
      ]);
      baixar = downloadAgain;
      setValue("db:downloadAgain:" + chave, baixar);
    }


    const spinner = ora(
      `Restaurando banco de dados para a chave: ${saveAs} ${emoji.get("hourglass_flowing_sand")}`,
    ).start();

    await restoreDatabase(chave, baixar, saveAs);
    spinner.text = `Reiniciando ZW ${emoji.get("hourglass_flowing_sand")}`;

    const containerZwName = await findZwContainer();
    let urlZw = null;
    if (containerZwName) {
      urlZw = await getContainerEnvVariable(containerZwName, "URL_ZW");
      await restartContainer(containerZwName);
      await healthCheck(urlZw + "/prest/health");

      spinner.text = `Executando migrate do banco do ZW ${emoji.get("hourglass_flowing_sand")}`;
      await migrateDbZw(urlZw, saveAs);
    }

    const containerTreinoName = await findTreinoContainer();
    if (containerTreinoName) {
      spinner.text = `Reiniciando Treino ${emoji.get("hourglass_flowing_sand")}`;
      await restartContainer(containerTreinoName);
      const urlTreino = await getContainerEnvVariable(
        containerTreinoName,
        "URL_TREINO",
      );
      await healthCheck(urlTreino + "/prest/health");
      spinner.text = `Executando migrate do banco do Treino ${emoji.get("hourglass_flowing_sand")}`;
      await migrateDbTreino(urlTreino, saveAs);
    }

    const containerLoginName = await findLoginContainer();
    if (containerLoginName) {
      spinner.text = `Reiniciando Login ${emoji.get("hourglass_flowing_sand")}`;
      await restartContainer(containerLoginName);
    }

    const containerDiscoveryName = await findDiscoveryContainer();
    if (containerDiscoveryName) {
      spinner.text = `Reiniciando Discovery ${emoji.get("hourglass_flowing_sand")}`;
      await restartContainer(containerDiscoveryName);
    }

    spinner.succeed(
      `Banco de dados restaurado com sucesso! ${emoji.get("white_check_mark")}`,
    );

    if(containerZwName){
      console.log(`Abrindo URL: ${urlZw}`);
      open(`${urlZw}`);
    }
  } catch (error) {
    if (spinner){
      spinner.fail(
        `Erro ao restaurar o banco de dados: ${err.message} ${emoji.get("x")}`,
      );
    }

    const msg = error && error.message ? error.message : error;
    console.log();
    console.error(
      `Erro ao executar o comando de banco de dados: ${msg}`,
    );
    
    process.exit(1);
  }
}

async function listDatabases() {
  const containerName = await findPostgresContainer();
  if (!containerName) {
    throw new Error("Postgres container not found");
  }

  return new Promise((resolve, reject) => {
    exec(
      `docker exec ${containerName} psql -U postgres -d OAMD -c "select chave, identificadorempresa from empresa where chave not in('testeAmericano');"`,
      (err, stdout, stderr) => {
        if (err) {
          return reject(err);
        }
        // A saída do psql possui cabeçalho, separador e rodapé. 
        // Exemplo de saída:
        //  chave | identificadorempresa
        // -------+----------------------
        //  teste | EMPRESA TESTE
        // (1 row)
        //
        const lines = stdout.split("\n");
        const dataLines = lines.filter(line => {
          const trimmed = line.trim();
          // Ignora linhas do cabeçalho, separadores e rodapé.
          return trimmed && 
                 !trimmed.startsWith("chave") &&
                 !trimmed.startsWith("-----") &&
                 !trimmed.startsWith("(");
        });
        // Mapeia cada linha para um objeto { chave, identificadorempresa }
        const companies = dataLines.map(line => {
          const parts = line.split("|").map(part => part.trim());
          return {
            chave: parts[0],
            identificadorempresa: parts[1]
          };
        });
        resolve(companies);
      }
    );
  });
}

module.exports = { runDatabaseCommand, listDatabases, migrateDbTreino, migrateDbZw };
