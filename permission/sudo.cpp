#include <boost/process.hpp>
#include <iostream>
#include <string>
#include <cstdlib>
#include <termios.h>
#include <unistd.h>

int main(int argc, char *argv[]) {
    if (argc < 2) {
        std::cerr << "Usage: " << argv[0] << " <command> [password]\n";
        return 1;
    }

    std::cout << "Executing command: " << argv[1] << std::endl;

    std::string password;
    if (argc == 3) {
        password = argv[2];
    } else {
        boost::process::child c("/bin/sh", "-c", "sudo -n true");
        c.wait();
        if (c.exit_code() != 0) {
            std::cout << "Password: ";
            std::flush(std::cout);

            // Desativar o eco
            termios oldt;
            tcgetattr(STDIN_FILENO, &oldt);
            termios newt = oldt;
            newt.c_lflag &= ~ECHO;
            tcsetattr(STDIN_FILENO, TCSANOW, &newt);

            // Ler a senha
            std::getline(std::cin, password);

            // Restaurar o eco
            tcsetattr(STDIN_FILENO, TCSANOW, &oldt);
        }
    }

#ifdef _WIN32
    boost::process::child c("runas", "/user:Administrator", argv[1]);
#else
    std::string command = "echo " + password + " | sudo -S true";
    try {
        boost::process::child c("/bin/sh", "-c", command.c_str());
        c.wait();
        if (c.exit_code() == 0) {
            boost::process::child c(argv[1]);
            c.wait();
        } else {
            std::cerr << "sudo failed\n";
            return 1;
        }
    } catch (boost::process::process_error& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
#endif

    return 0;
}