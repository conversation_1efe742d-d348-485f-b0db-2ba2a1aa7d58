ENVIRONMENT=dev
PROFILE=hml
SERVER_PORT=8080
DEBUG=true
DEBUG_DEPLOY=false
STACK_NAME=${STACK_NAME}
IP_HOST=${STACK_NAME}_postgres
URL_DATABASE_OAMD=jdbc:postgresql://${STACK_NAME}_postgres:5432/OAMD
DB_OAMD2_URL=jdbc:postgresql://${STACK_NAME}_postgres:5432/OAMD2
DB_URL=jdbc:postgresql://${STACK_NAME}_postgres:5432/OAMD
OAMD_URL=jdbc:postgresql://${STACK_NAME}_postgres:5432/OAMD
BDZILLYON_VERSION=2050
POSTGRES_MAX_CONNECTIONS=1000
MENCACHE_HOST=${STACK_NAME}_memcached:11211
CONTEXTO=
AMAZON_DYNAMODB_ACCESSKEY=AMAZON_DYNAMODB_ACCESSKEY
AMAZON_DYNAMODB_REGION=
AMAZON_DYNAMODB_SECRETKEY=wzkere
ENABLE_CAPTCHA=false
ENABLE_CAPTCHA_USER_OAMD=false
URL_DYNAMODB=http://${STACK_NUMBER}-dynamodb.${DOMAIN}
URL_POSTGRES_API=http://${STACK_NUMBER}-pg.${DOMAIN}
HEALTHCHECK_KEY=teste
INIT_DB=teste
UPLOAD_TO_NUVEM=false
VIRTUAL_PORT=8080
JAVA_OPTS=-Duser.timezone=America/Sao_Paulo -Duser.language=pt -Duser.region=BR

# Proxy 
URL_TRAEFIK=http://traefik.${DOMAIN}

# Git
CI_COMMIT_REF_SLUG=${CI_COMMIT_REF_SLUG}

# Test
CYPRESS_baseUrl=http://${STACK_NUMBER}-zw.${DOMAIN}/ZillyonWeb
CYPRESS_CI_BUILD_ID=${CYPRESS_CI_BUILD_ID}
COMMIT_INFO_BRANCH=${CI_COMMIT_REF_SLUG}
CYPRESS_RECORD_KEY=14f6a232-b08c-401b-ac42-cf1e51fee68e
CYPRESS_SPEC=
CYPRESS_REPLICAS=2
CYPRESS_SORRY=true
TEST_ENVIRONMENT=PIPE
URL_CYPRESS_SORRY_DIRECTOR=http://director-cypress.pactoteste.com

# Service zw
URL_OAMD=http://${STACK_NUMBER}-oamd.${DOMAIN}/NewOAMD
SMTP_EMAIL_ROBO=<EMAIL>
SMTP_EMAIL_NOREPLY=<EMAIL>
SMTP_LOGIN_ROBO=<EMAIL>
SMTP_SENHA_ROBO=**************************************************
SMTP_SERVER_ROBO=smtp.mailgun.org
SMTP_SERVER_ROBO_INICIAR_TLS=true
SMTP_SERVER_ROBO_CONEXAO_SEGURA=false
URL_MAILING=http://${STACK_NUMBER}-zw.${DOMAIN}/ZillyonWeb/ms
URL_JENKINS=http://${STACK_NUMBER}-jenkins.${DOMAIN}
USAR_URL_RECURSO_EMPRESA=false
ENABLE_MENU_ZW_UI=true
HABILITAR_NICHO=false
HABILITAR_CACHE_INIT_NICHO=false
VALIDADE_CACHE_NICHO_EM_MINUTOS=0
USAR_URL_RECURSO_EMPRESA=false

# Legacies 
URL_ZW=http://${STACK_NUMBER}-zw.${DOMAIN}/ZillyonWeb
URL_ZW_INTEGRACAO=http://${STACK_NUMBER}-zw.${DOMAIN}/ZillyonWeb
URL_TREINO=http://${STACK_NUMBER}-treino-api.${DOMAIN}/TreinoWeb
URL_OAMD=http://${STACK_NUMBER}-oamd.${DOMAIN}/NewOAMD
URL_ZW_API=http://${STACK_NUMBER}-api.${DOMAIN}/API-ZillyonWeb
URL_API=http://${STACK_NUMBER}-api.${DOMAIN}/API-ZillyonWeb
URL_LOGIN=http://${STACK_NUMBER}-login-api.${DOMAIN}/LoginApp
URL_JENKINS=http://${STACK_NUMBER}-jenkins.${DOMAIN}
URL_GAME=http://${STACK_NUMBER}-game.${DOMAIN}/GameofResults

# Microservices=
URL_AUTENTICACAO=http://${STACK_NUMBER}-autenticacao.${DOMAIN}
DISCOVERY_URL=http://${STACK_NUMBER}-discovery.${DOMAIN}
URL_DISCOVERY=http://${STACK_NUMBER}-discovery.${DOMAIN}
URL_ARAGORN=http://${STACK_NUMBER}-aragorn.${DOMAIN}
URL_GRADUACAO=http://${STACK_NUMBER}-graduacao.${DOMAIN}
URL_RELATORIO_FULL=http://${STACK_NUMBER}-relatorio-full.${DOMAIN}
URL_CLIENTE_MS=http://${STACK_NUMBER}-cliente.${DOMAIN}
URL_BI_MS=http://${STACK_NUMBER}-bi.${DOMAIN}
URL_MEDIA_MS=http://${STACK_NUMBER}-media.${DOMAIN}
URL_CAD_AUX_MS=http://${STACK_NUMBER}-cad-aux.${DOMAIN}
URL_CONTATO_MS=http://${STACK_NUMBER}-crm-ms.${DOMAIN}/crm-ms
URL_CRM_MS=http://${STACK_NUMBER}-crm-ms.${DOMAIN}/crm-ms
URL_ADM_CORE_MS=http://${STACK_NUMBER}-adm-core.${DOMAIN}
URL_PERSONAGEM=http://${STACK_NUMBER}-personagem.${DOMAIN}
URL_PESSOA_MS=http://${STACK_NUMBER}-pessoa.${DOMAIN}
URL_PACTOPAY_DASH=http://${STACK_NUMBER}-pactopay-dash.${DOMAIN}
URL_PLANO_MS=http://${STACK_NUMBER}-plano.${DOMAIN}
URL_PRODUTO_MS=http://${STACK_NUMBER}-produto.${DOMAIN}
URL_PACTOPAY_MS=http://${STACK_NUMBER}-pactopay.${DOMAIN}
URL_ACESSO_SISTEMA_MS=http://${STACK_NUMBER}-acesso.${DOMAIN}
URL_ADM_MS=http://${STACK_NUMBER}-adm-ms.${DOMAIN}/adm-ms
URL_MIDIASOCIAL_MS=http://${STACK_NUMBER}-midia-social.${DOMAIN}
URL_MAKETING_MS=http://${STACK_NUMBER}-marketing.${DOMAIN}
URL_MENU_MS=http://${STACK_NUMBER}-menu.${DOMAIN}
URL_NOTIFICACAO_MS=http://${STACK_NUMBER}-notificacao.${DOMAIN}
URL_FINANCEIRO_MS=http://${STACK_NUMBER}-financeiro-ms.${DOMAIN}/financeiro-ms
URL_RECURSO_MS=http://${STACK_NUMBER}-recurso.${DOMAIN}/recurso-ms
URL_RELATORIO_MS=http://${STACK_NUMBER}-relatorio.${DOMAIN}
URL_CLUBE_VANTAGENS_MS=http://${STACK_NUMBER}-clube-vantagens.${DOMAIN}

# Fronts 
URL_NOVO_TREINO=http://${STACK_NUMBER}-treino.${DOMAIN}
URL_VENDAS_2=http://${STACK_NUMBER}-vendas.${DOMAIN}
URL_VENDAS_ONLINE=http://${STACK_NUMBER}-vendas.${DOMAIN}
URL_HTTP_PLATAFORMA_PACTO=http://${STACK_NUMBER}-canal.${DOMAIN}
URL_ADM=http://${STACK_NUMBER}-adm.${DOMAIN}
URL_ZW_FRONT=http://${STACK_NUMBER}-adm.${DOMAIN}
URL_NOVO_LOGIN=http://${STACK_NUMBER}-login.${DOMAIN}


