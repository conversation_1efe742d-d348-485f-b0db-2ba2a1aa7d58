version: "3"

services:
  
  aragorn-ms:
    image: registry.gitlab.com/pactopay/aragorn:master
    networks:
      - test
    env_file:
      - .env
    deploy:
      labels:
        - "traefik.enable=true"
        - "traefik.http.routers.${STACK_NAME}_aragorn-ms.rule=Host(`${STACK_NUMBER}-aragorn.${DOMAIN}`)"
        - "traefik.http.services.${STACK_NAME}_aragorn-ms.loadbalancer.server.port=8080"
        - "traefik.http.services.${STACK_NAME}_aragorn-ms.loadbalancer.passhostheader=true"

networks:
  test:
    external: true
