version: "3"

services:

  vendas-online-front:
    image: registry.gitlab.com/plataformazw/vendasonline-v2.0:master
    networks:
      - test
    env_file:
      - .env
    environment:
      URL_API: http://${STACK_NUMBER}-api.${DOMAIN}/API-ZillyonWeb/prest
    deploy:
      labels:
        - "traefik.enable=true"
        - "traefik.http.routers.${STACK_NAME}_vendas-online-front.rule=Host(`${STACK_NUMBER}-vendas.${DOMAIN}`)"
        - "traefik.http.services.${STACK_NAME}_vendas-online-front.loadbalancer.server.port=80"
        - "traefik.http.services.${STACK_NAME}_vendas-online-front.loadbalancer.passhostheader=true"
  treino-front:
    image: registry.gitlab.com/plataformazw/zw_ui/novo-treino:master
    networks:
      - test
    env_file:
      - .env
    deploy:
      labels:
        - "traefik.enable=true"
        - "traefik.http.routers.${STACK_NAME}_treino-front.rule=Host(`${STACK_NUMBER}-treino.${DOMAIN}`)"
        - "traefik.http.services.${STACK_NAME}_treino-front.loadbalancer.server.port=80"
        - "traefik.http.services.${STACK_NAME}_treino-front.loadbalancer.passhostheader=true"
  adm-front:
    image: registry.gitlab.com/plataformazw/zw_ui/adm:master
    networks:
      - test
    env_file:
      - .env
    deploy:
      labels:
        - "traefik.enable=true"
        - "traefik.http.routers.${STACK_NAME}_adm-front.rule=Host(`${STACK_NUMBER}-adm.${DOMAIN}`)"
        - "traefik.http.services.${STACK_NAME}_adm-front.loadbalancer.server.port=80"
        - "traefik.http.services.${STACK_NAME}_adm-front.loadbalancer.passhostheader=true"
  login-front:
    image: registry.gitlab.com/plataformazw/zw_ui/login:master
    networks:
      - test
    env_file:
      - .env
    deploy:
      labels:
        - "traefik.enable=true"
        - "traefik.http.routers.${STACK_NAME}_login-front.rule=Host(`${STACK_NUMBER}-login.${DOMAIN}`)"
        - "traefik.http.services.${STACK_NAME}_login-front.loadbalancer.server.port=80"
        - "traefik.http.services.${STACK_NAME}_login-front.loadbalancer.passhostheader=true"
 
networks:
  test:
    external: true