version: "3"

services:
  # Legacies
  zw:
    image: registry.gitlab.com/plataformazw/zw/tomcat:master
    networks:
      - test
    env_file:
      - .env
    deploy:
      labels:
        - "traefik.enable=true"
        - "traefik.http.routers.${STACK_NAME}_zw.rule=Host(`${STACK_NUMBER}-zw.${DOMAIN}`)"
        - "traefik.http.services.${STACK_NAME}_zw.loadbalancer.server.port=8080"
        - "traefik.http.services.${STACK_NAME}_zw.loadbalancer.passhostheader=true"
        # - "traefik.http.services.${STACK_NAME}_zw.loadBalancer.sticky.cookie=true"
        # - "traefik.http.services.${STACK_NAME}_zw.loadBalancer.sticky.cookie.name=sticky.session"
      

  treino:
    image: registry.gitlab.com/plataformazw/treino/tomcat:master
    networks:
      - test
    env_file:
      - .env
    deploy:
      labels:
        - "traefik.enable=true"
        - "traefik.http.routers.${STACK_NAME}_treino.rule=Host(`${STACK_NUMBER}-treino-api.${DOMAIN}`)"
        - "traefik.http.services.${STACK_NAME}_treino.loadbalancer.server.port=8080"
        - "traefik.http.services.${STACK_NAME}_treino.loadbalancer.passhostheader=true"
      
  oamd:
    image: registry.gitlab.com/plataformazw/oamd/tomcat:master
    networks:
      - test
    env_file:
      - .env
    deploy:
      labels:
        - "traefik.enable=true"
        - "traefik.http.routers.${STACK_NAME}_oamd.rule=Host(`${STACK_NUMBER}-oamd.${DOMAIN}`)"
        - "traefik.http.services.${STACK_NAME}_oamd.loadbalancer.server.port=8080"
        - "traefik.http.services.${STACK_NAME}_oamd.loadbalancer.passhostheader=true"

  api:
    image: registry.gitlab.com/plataformazw/api:master
    networks:
      - test
    env_file:
      - .env
    deploy:
      labels:
        - "traefik.enable=true"
        - "traefik.http.routers.${STACK_NAME}_api.rule=Host(`${STACK_NUMBER}-api.${DOMAIN}`)"
        - "traefik.http.services.${STACK_NAME}_api.loadbalancer.server.port=8080"
        - "traefik.http.services.${STACK_NAME}_api.loadbalancer.passhostheader=true"
  login:
    image: registry.gitlab.com/plataformazw/login:master
    networks:
      - test
    env_file:
      - .env
    deploy:
      labels:
        - "traefik.enable=true"
        - "traefik.http.routers.${STACK_NAME}_login.rule=Host(`${STACK_NUMBER}-login-api.${DOMAIN}`)"
        - "traefik.http.services.${STACK_NAME}_login.loadbalancer.server.port=8080"
        - "traefik.http.services.${STACK_NAME}_login.loadbalancer.passhostheader=true"


networks:
  test:
    external: true