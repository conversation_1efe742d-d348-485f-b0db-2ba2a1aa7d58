version: "3"

services:

  postgres:
    image: registry.gitlab.com/plataformazw/docker-pacto/postgres:9.4
    environment: 
      VIRTUAL_HOST: ${STACK_NUMBER}-pg.${DOMAIN}
      POSTGRES_RESTORE_JOBS: 23
    networks:
      - test
    env_file:
      - .env
    deploy:
      labels:
        - "traefik.enable=true"
        - "traefik.http.routers.${STACK_NAME}_postgres.rule=Host(`${STACK_NUMBER}-pg.${DOMAIN}`)"
        - "traefik.http.services.${STACK_NAME}_postgres.loadbalancer.server.port=8080"
        - "traefik.http.services.${STACK_NAME}_postgres.loadbalancer.passhostheader=true"
  dynamodb:
    image: amazon/dynamodb-local:1.22.0
    command:
     - -jar
     - DynamoDBLocal.jar
     - -sharedDb
     - -dbPath
     - ./
    networks:
      - test
    env_file:
      - .env
    deploy:
      labels:
        - "traefik.enable=true"
        - "traefik.http.routers.${STACK_NAME}_dynamodb.rule=Host(`${STACK_NUMBER}-dynamodb.${DOMAIN}`)"
        - "traefik.http.services.${STACK_NAME}_dynamodb.loadbalancer.server.port=8000"
        - "traefik.http.services.${STACK_NAME}_dynamodb.loadbalancer.passhostheader=true"

networks:
  test:
    external: true
  