version: "3.8"

services:
  traefik:
    image: traefik:2.11
    command:
      - "--entryPoints.web.address=:80"
      - "--entryPoints.websecure.address=:443"
      - "--log.level=INFO"
      - "--accesslog=true"
      - "--accesslog.format=json"
      - "--providers.docker=true"
      - "--providers.docker.watch=true"
      - "--providers.docker.swarmmode=true"
      - "--providers.docker.network=test"
      - "--providers.docker.endpoint=unix:///var/run/docker.sock"
      - "--providers.docker.swarmModeRefreshSeconds=5s"
      - "--api"
      - "--providers.docker.exposedbydefault=false"
      - "--providers.file.directory=/config/"
      - "--providers.file.watch=true"
      # Create mechanism to generate certifiacte called "LE"
      # - "--certificatesresolvers.le.acme.tlschallenge=true"
      # - "--certificatesresolvers.le.acme.dnschallenge.delaybeforecheck=60"
      # - "--certificatesresolvers.le.acme.email=<EMAIL>"
      # Store obtained certificates on the given storage
      # - "--certificatesresolvers.le.acme.storage=/le/acme.json"
    networks:
      - test
    ports:
      - "80:80"
      - "443:443"
    deploy:
      replicas: 1
      restart_policy:
        condition: on-failure
      placement:
        constraints:
          - node.role == manager
      labels:
        - "traefik.enable=true" # Habilita o uso do Traefik
        - "traefik.http.routers.t.rule=Host(`traefik.${DOMAIN}`)" # Diga ao Traefik para criar a rota 't' e capturar todas as solicitações com o Host fornecido (cabeçalho http: Host)
        - "traefik.http.routers.t.service=api@internal"
        # - "traefik.http.routers.t.tls.certresolver=le" # Use o certificado gerado pelo mecanismo de resolução de certificado chamado "le"
        - "traefik.http.routers.t.entrypoints=websecure,web" 
        - "traefik.http.services.t.loadbalancer.server.port=8080"
        # - "traefik.http.services.t.loadbalancer.passhostheader=true" # Para habilitar autenticação no traefik
        # - "traefik.http.routers.t.middlewares=authtraefik" 
        # - "traefik.http.middlewares.authtraefik.basicauth.users=admin:$$2y$$05$$1OX5jZ1Kpm/iVKE8tgUhu.STmPkgi0lLxVeP5yEcRioFdV4mcgdTu" #  admin/adiin
        # - "traefik.http.routers.http-catchall.rule=hostregexp(`{host:.+}`)" 
        # - "traefik.http.routers.http-catchall.entrypoints=web"
        # - "traefik.http.routers.http-catchall.middlewares=redirect-to-https"
        # - "traefik.http.middlewares.redirect-to-https.redirectscheme.scheme=https"
    configs:
      - source: traefik-config
        target: /config/traefik.yaml
      - source: cert-pacto-crt
        target: /certs/pactosolucoes.com.br.crt
      - source: cert-pacto-key
        target: /certs/pactosolucoes.com.br.key
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      # Se utilizar certificado com le é melhor mapear o volume para armazenar os certificados
      # - type: bind
      #   source: /mnt/traefik/le
      #   target: /le
      # - type: bind
      #   source: /mnt/traefik/config
      #   target: /config
      # - type: bind
      #   source: /mnt/traefik/certs
      #   target: /certs

networks:
  test:
    external: true

configs:
  traefik-config:
    external: true
  cert-pacto-crt:
    file: ./traefik/certs/pactosolucoes.com.br.crt
  cert-pacto-key:
    file: ./traefik/certs/pactosolucoes.com.br.key