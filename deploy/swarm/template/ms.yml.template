version: "3"

services:
  autenticacao-ms:
    image: registry.gitlab.com/plataformazw/autenticacao:master
    networks:
      - test
    env_file:
      - .env
    environment:
      DB_URL: jdbc:postgresql://${STACK_NAME}_postgres:5432/autenticacao
    deploy:
      labels:
        - "traefik.enable=true"
        - "traefik.http.routers.${STACK_NAME}_autenticacao-ms.rule=Host(`${STACK_NUMBER}-autenticacao.${DOMAIN}`)"
        - "traefik.http.services.${STACK_NAME}_autenticacao-ms.loadbalancer.server.port=8080"
        - "traefik.http.services.${STACK_NAME}_autenticacao-ms.loadbalancer.passhostheader=true"

  discovery-ms:
    image: registry.gitlab.com/plataformazw/discovery-urls:master
    networks:
      - test
    env_file:
      - .env
    deploy:
      labels:
        - "traefik.enable=true"
        - "traefik.http.routers.${STACK_NAME}_discovery-ms.rule=Host(`${STACK_NUMBER}-discovery.${DOMAIN}`)"
        - "traefik.http.services.${STACK_NAME}_discovery-ms.loadbalancer.server.port=8080"
        - "traefik.http.services.${STACK_NAME}_discovery-ms.loadbalancer.passhostheader=true"
  
  relatorio-full-ms:
    image: registry.gitlab.com/plataformazw/relatorio-full:master
    networks:
      - test
    env_file:
      - .env
    deploy:
      labels:
        - "traefik.enable=true"
        - "traefik.http.routers.${STACK_NAME}_relatorio-full-ms.rule=Host(`${STACK_NUMBER}-relatorio-full.${DOMAIN}`)"
        - "traefik.http.services.${STACK_NAME}_relatorio-full-ms.loadbalancer.server.port=8080"
        - "traefik.http.services.${STACK_NAME}_relatorio-full-ms.loadbalancer.passhostheader=true"

  bi-ms:
    image: registry.gitlab.com/plataformazw/bi-ms:master
    networks:
      - test
    env_file:
      - .env
    deploy:
      labels:
        - "traefik.enable=true"
        - "traefik.http.routers.${STACK_NAME}_bi-ms.rule=Host(`${STACK_NUMBER}-bi.${DOMAIN}`)"
        - "traefik.http.services.${STACK_NAME}_bi-ms.loadbalancer.server.port=8080"
        - "traefik.http.services.${STACK_NAME}_bi-ms.loadbalancer.passhostheader=true"


  cad-aux-ms:
    image: registry.gitlab.com/plataformazw/cad-aux-ms:master
    networks:
      - test
    env_file:
      - .env
    deploy:
      labels:
        - "traefik.enable=true"
        - "traefik.http.routers.${STACK_NAME}_cad-aux-ms.rule=Host(`${STACK_NUMBER}-cad-aux.${DOMAIN}`)"
        - "traefik.http.services.${STACK_NAME}_cad-aux-ms.loadbalancer.server.port=8080"
        - "traefik.http.services.${STACK_NAME}_cad-aux-ms.loadbalancer.passhostheader=true"

  plano-ms:
    image: registry.gitlab.com/plataformazw/plano-java-ms:master
    environment:
      VIRTUAL_HOST: 
    networks:
      - test
    env_file:
      - .env
    deploy:
      labels:
        - "traefik.enable=true"
        - "traefik.http.routers.${STACK_NAME}_plano-ms.rule=Host(`${STACK_NUMBER}-plano.${DOMAIN}`)"
        - "traefik.http.services.${STACK_NAME}_plano-ms.loadbalancer.server.port=8080"
        - "traefik.http.services.${STACK_NAME}_plano-ms.loadbalancer.passhostheader=true"

  adm-core-ms:
    image: registry.gitlab.com/plataformazw/adm-core-ms:master
    environment:
      VIRTUAL_HOST: 
    networks:
      - test
    env_file:
      - .env
    deploy:
      labels:
        - "traefik.enable=true"
        - "traefik.http.routers.${STACK_NAME}_adm-core-ms.rule=Host(`${STACK_NUMBER}-adm-core.${DOMAIN}`)"
        - "traefik.http.services.${STACK_NAME}_adm-core-ms.loadbalancer.server.port=8080"
        - "traefik.http.services.${STACK_NAME}_adm-core-ms.loadbalancer.passhostheader=true"

  crm-ms:
    image: registry.gitlab.com/plataformazw/flash/crm-ms:master
    networks:
      - test
    env_file:
      - .env
    deploy:
      labels:
        - "traefik.enable=true"
        - "traefik.http.routers.${STACK_NAME}_crm-ms.rule=Host(`${STACK_NUMBER}-crm-ms.${DOMAIN}`)"
        - "traefik.http.services.${STACK_NAME}_crm-ms.loadbalancer.server.port=8080"
        - "traefik.http.services.${STACK_NAME}_crm-ms.loadbalancer.passhostheader=true"

  adm-ms:
    image: registry.gitlab.com/plataformazw/adm-ms:master
    networks:
      - test
    env_file:
      - .env
    deploy:
      labels:
        - "traefik.enable=true"
        - "traefik.http.routers.${STACK_NAME}_adm-ms.rule=Host(`${STACK_NUMBER}-adm-ms.${DOMAIN}`)"
        - "traefik.http.services.${STACK_NAME}_adm-ms.loadbalancer.server.port=8080"
        - "traefik.http.services.${STACK_NAME}_adm-ms.loadbalancer.passhostheader=true"

  produto-ms:
    image: registry.gitlab.com/plataformazw/produto-ms:master
    networks:
      - test
    env_file:
      - .env
    deploy:
      labels:
        - "traefik.enable=true"
        - "traefik.http.routers.${STACK_NAME}_produto-ms.rule=Host(`${STACK_NUMBER}-produto.${DOMAIN}`)"
        - "traefik.http.services.${STACK_NAME}_produto-ms.loadbalancer.server.port=8080"
        - "traefik.http.services.${STACK_NAME}_produto-ms.loadbalancer.passhostheader=true"

  clube-vantagens-ms:
    image: registry.gitlab.com/plataformazw/clube-vantagens-ms:master
    networks:
      - test
    env_file:
      - .env
    deploy:
      labels:
        - "traefik.enable=true"
        - "traefik.http.routers.${STACK_NAME}_clube-vantagens-ms.rule=Host(`${STACK_NUMBER}-clube-vantagens.${DOMAIN}`)"
        - "traefik.http.services.${STACK_NAME}_clube-vantagens-ms.loadbalancer.server.port=8080"
        - "traefik.http.services.${STACK_NAME}_clube-vantagens-ms.loadbalancer.passhostheader=true"

  pessoa-ms:
    image: registry.gitlab.com/plataformazw/pessoa-ms:master
    networks:
      - test
    env_file:
      - .env
    deploy:
      labels:
        - "traefik.enable=true"
        - "traefik.http.routers.${STACK_NAME}_pessoa-ms.rule=Host(`${STACK_NUMBER}-pessoa.${DOMAIN}`)"
        - "traefik.http.services.${STACK_NAME}_pessoa-ms.loadbalancer.server.port=8080"
        - "traefik.http.services.${STACK_NAME}_pessoa-ms.loadbalancer.passhostheader=true"


networks:
  test:
    external: true
