# Configs para rotas locais
http:
  routers:
    jenkins-in-host:
      rule: PathPrefix(`/jk`)
      service: "jenkins-in-host"
      middlewares:
      # - "jenkins-stripprefix"
      - "https-redirect"
      tls: {}
      entryPoints:
      - "websecure"
      - "web"
    jenkins-ms:
      rule: PathPrefix(`/jk-ms`)
      service: "jenkins-ms"
      middlewares:
        - "https-redirect"
      tls: {}
      entryPoints:
        - "websecure"
        - "web"
  services:
    # Jenkins do node z2-srv02 (*********) do cluster pipe-zw 
    jenkins-in-host:
      loadBalancer:
        servers:
        - url: "http://***********:8444"
    jenkins-ms:
      loadBalancer:
        servers:
          - url: "http://**********:8445"
  middlewares:
    # jenkins-stripprefix:
    #   stripPrefix:
    #     prefixes:
    #     - "/jk"
    https-redirect:
      redirectScheme:
        scheme: https
        permanent: true

tls:
  stores:
    default:
      defaultCertificate:
        certFile: /certs/pactosolucoes.com.br.crt
        keyFile: /certs/pactosolucoes.com.br.key