const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const { getNormalizedBranchName, normalizeBranchName } = require("../../git/branch.js");
const { root } = require('../../util/path.js');
const yaml = require('js-yaml');
const { configDotenv } = require('dotenv');
const { log } = require('../../util/log.js');
const url = require('url');
const { walkDir } = require('../../util/path.js');
const { addDomainsToHost } = require('../../network/host.js');
const { isRoot } = require('../../util/user.js');
const waitOn = require('wait-on');
const axios = require('axios');
const emoji = require('node-emoji');
const ora = require('ora');
const spinner = ora();

async function deploy(stackNumber, domain, ip, ciBuildId, branchName, deployEnvironment, serviceBranchs, filterServiceNames) {

    checkAndCreateSwarmCluster();

    const cypressCiBuildId = ciBuildId || stackNumber;
    const stackName = `test-${stackNumber}`;
    const urlAragornEnvLocal = 'http://squad-migracao.pactosolucoes.com.br:8102';
    const ciCommitRefSlug = branchName || getNormalizedBranchName();

    const envTemplatePath = path.resolve(root(), 'deploy/swarm/template/.env.template');
    const envPath = path.resolve(root(), 'deploy/swarm/template/.env');

    fs.copyFileSync(envTemplatePath, envTemplatePath);

    const stacks = execSync('docker stack ls').toString();
    if (stacks.includes(stackName)) {
        spinner.info(`A stack ${stackName} já existe. Eu vou exclui-la para criar uma nova...`);
        remove(stackName);
    }

    spinner.start('Compilando templates...');

    copyTemplatesToYml();

    replaceEnvironmentVariables(stackNumber,
        domain, 
        cypressCiBuildId, 
        ciCommitRefSlug, 
        urlAragornEnvLocal);

    replaceServiceTagsInComposeFiles(serviceBranchs);

    replaceServiceNamesInComposeFiles(filterServiceNames);

    const debugCommand = process.env.DEBUG;
    configDotenv({ path: envPath});
    process.env.DEBUG = debugCommand;
    log('pacto:deploy', 'Variáveis de ambiente configuradas:', process.env)
    await addExtraHostsFromEnvToComposeFiles(ip, deployEnvironment);

    spinner.succeed('Templates compilados com sucesso!');

    spinner.start('Iniciando deploy do proxy e rede...');
    const networks = execSync('docker network ls').toString();
    if (!networks.includes('test')) {
        console.info("Criando rede test...");
        execSync('docker network create -d overlay --attachable test');
    }

    try {
        process.chdir(path.resolve(getTemplateDirPath()));
    } catch (err) {
        console.error(`Erro ao mudar de diretorio: ${err}`);
    }

    const services = execSync('docker service ls').toString();
    if (!services.includes('proxy')) {
        const configs = execSync('docker config ls').toString();
        log('pacto:deploy',"Criando traefik config...");
        if(configs.includes('traefik-config')){
            const resultRemoveConfig = execSync('docker config rm traefik-config').toString();
            log('pacto:deploy', 'Removido config autal. Resultado do comando',resultRemoveConfig);
        }

        const configFileName = deployEnvironment.trim().toLowerCase();
        const traefikConfigFile = `traefik/config/${configFileName}.yaml`;
        const traefikConfigPath = path.resolve(getTemplateDirPath(), traefikConfigFile);
        log('pacto:deploy', `Criando traefik config com arquivo ${traefikConfigFile}...`);
        const resultCreateConfig = execSync(`docker config create traefik-config ${traefikConfigPath}`).toString();
        log('pacto:deploy', 'Resultado do comando', resultCreateConfig);

        log('pacto:deploy', 'Iniciando deploy do proxy...');
        const proxyStackFilePath = path.resolve(getTemplateDirPath(), 'proxy.yml');
        const resultProxyDeploy = execSync(`docker stack deploy  -c ${proxyStackFilePath} proxy`).toString();
        log('pacto:deploy', 'Resultado do comando de deploy', resultProxyDeploy);
    }

    spinner.succeed('Proxy e rede configurados com sucesso!');

    spinner.start("Realizando deploy da base de dados...");
    const databasesFilePath = path.resolve(getTemplateDirPath(), 'databases.yml');
    const comandDeployDatabase = `docker stack deploy  -c ${databasesFilePath} ${stackName} --with-registry-auth --resolve-image always`;
    log('pacto:deploy', 'Deploying database stack command...', comandDeployDatabase)
    const resultDatabases = execSync(comandDeployDatabase).toString();
    log('pacto:deploy', 'Resultado do comando de deploy', resultDatabases);
    await wait(`${process.env.URL_POSTGRES_API}/health/${process.env.INIT_DB}`, 'postgres', null, filterServiceNames);
    spinner.succeed('Base de dados iniciada com sucesso!');

    spinner.start("Realizando deploy das aplicações legadas...");
    const legacyFilePath = path.resolve(getTemplateDirPath(), 'legacy.yml');
    const resultLegacy = execSync(`docker stack deploy  -c ${legacyFilePath} ${stackName} --with-registry-auth --resolve-image always`).toString();
    log('pacto:deploy', 'Resultado do comando de deploy', resultLegacy);

    await wait(`${process.env.URL_ZW}/prest/health`, 'zw', null, filterServiceNames);
    await wait(`${process.env.URL_TREINO}/prest/health`, 'treino', null, filterServiceNames);
    await wait(`${process.env.URL_OAMD}/prest/health`, 'oamd', null, filterServiceNames);
    // #TODO: Descomentar após ajustar o health check do login
    // await wait(`${process.env.URL_LOGIN}/prest/health`, 'login', null, filterServiceNames);

    await migrateZw(process.env.URL_ZW, process.env.INIT_DB);
    await migrateTreino(process.env.URL_TREINO, process.env.INIT_DB);

    await wait(`${process.env.URL_ZW}/prest/versao`, 'zw', { chave: process.env.INIT_DB }, filterServiceNames);
    await wait(`${process.env.URL_TREINO}/prest/health/${process.env.INIT_DB}`, 'treino', null, filterServiceNames);
    await wait(`${process.env.URL_OAMD}/prest/health/${process.env.INIT_DB}`, 'oamd', null, filterServiceNames);
    // #TODO: Descomentar após ajustar o health check do login 
    // await wait(`${process.env.URL_LOGIN}/prest/health/${process.env.INIT_DB}`, 'login', null, filterServiceNames);

    spinner.succeed('Aplicações legadas iniciadas com sucesso!');

    spinner.start("Realizando deploy dos micro serviços...");
    const msFilePath = path.resolve(getTemplateDirPath(), 'ms.yml');
    const resultMicroservices = execSync(`docker stack deploy  -c ${msFilePath} ${stackName} --with-registry-auth --resolve-image always`).toString();
    log('pacto:deploy', 'Resultado do comando de deploy', resultMicroservices);

    if (isDeployEnvironmentCi(deployEnvironment)) {
        log('pacto:deploy', 'Iniciando deploy do aragorn-ms...');
        const aragornFilePath = path.resolve(getTemplateDirPath(), 'ms.aragorn.yml');
        execSync(`docker stack deploy  -c ${aragornFilePath} ${stackName} --with-registry-auth --resolve-image always`).toString();
        log('pacto:deploy', 'Resultado do comando de deploy', resultMicroservices);
    }

    await wait(`${process.env.DISCOVERY_URL}/health`, 'discovery-ms', null, filterServiceNames);
    await wait(`${process.env.URL_AUTENTICACAO}/health`, 'bi-ms', null, filterServiceNames);
    await wait(`${process.env.URL_RELATORIO_FULL}/health`, 'relatorio-full-ms', null, filterServiceNames);
    await wait(`${process.env.URL_BI_MS}/health`, 'bi-ms', null, filterServiceNames);
    await wait(`${process.env.URL_ADM_CORE_MS}/health`, 'adm-core-ms', null, filterServiceNames);
    await wait(`${process.env.URL_CAD_AUX_MS}/health`, 'cad-aux-ms', null, filterServiceNames);
    await wait(`${process.env.URL_PLANO_MS}/health`, 'plano-ms', null, filterServiceNames);
    await wait(`${process.env.URL_CRM_MS}/v1/health`, 'crm-ms', null, filterServiceNames);
    await wait(`${process.env.URL_ADM_MS}/v1/health`, 'adm-ms', null, filterServiceNames);
    await wait(`${process.env.URL_PRODUTO_MS}/health`, 'produto-ms', null, filterServiceNames);

    spinner.succeed('Micro serviços iniciados com sucesso!');

    spinner.start("Realizando deploy dos front-ends...");
    const resultFrontendDeploy = execSync(`docker stack deploy  -c front.yml ${stackName} --with-registry-auth --resolve-image always`).toString();
    log('pacto:deploy', 'Resultado do comando de deploy', resultFrontendDeploy);

    await wait(`${process.env.URL_NOVO_TREINO}`, 'treino-front', null, filterServiceNames);
    await wait(`${process.env.URL_ZW_FRONT}`, 'adm-front', null, filterServiceNames);
    await wait(`${process.env.URL_NOVO_LOGIN}`, 'login-front', null, filterServiceNames);

    spinner.succeed('Front-ends iniciados com sucesso!');

    spinner.start("Aplicando correções de ambiente...");
    fixZwDatabaseMigration(stackName);
    spinner.succeed('Correções pós deploy aplicadas com sucesso!');

    console.info("Tudo certo papai...", emoji.get("ok_hand"));
    console.info("Seja feliz", emoji.get("smile"));
}

function getSwarmServiceGoupNames(){
    return getSwarmServiceGroups().map(group => group.name);
}

function getSwarmServicesGroupByName(name){
    return name === 'Todos' ? null : getSwarmServiceGroups().find(group => group.name === name).services;
}

function getSwarmServiceGroups(){
    return [
        {
            name: 'Adm',
            services: [ 'postgres', 'dynamodb', 'traefik', 'zw', 'treino', 'oamd', 'login', 'autenticacao-ms', 'discovery-ms', 'adm-core-ms', 'adm-ms', 'adm-front', 'login-front']
        },
        {
            name: 'Treino',
            services: ['postgres', 'dynamodb', 'traefik', 'zw', 'treino', 'oamd', 'login', 'autenticacao-ms', 'discovery-ms', 'treino-front', 'login-front']
        },
        {
            name: 'Todos'
        }
    ];
}

function checkAndCreateSwarmCluster(){
    const dockerInfo = execSync('docker info --format "{{.Swarm.LocalNodeState}}"').toString().trim();

    if (dockerInfo !== 'active') {
        execSync('docker swarm init');
        spinner.info('Docker Swarm cluster iniciado.');
    }
}

function isDeployEnvironmentLocal(deployEnvironment) {
    return deployEnvironment.trim().toLowerCase() === 'local';
}

function isDeployEnvironmentCi(deployEnvironment) {
    return deployEnvironment.trim().toLowerCase() === 'ci';
}

function replaceServiceNamesInComposeFiles(filterServiceNames) {
    if(filterServiceNames === undefined || filterServiceNames === '' || filterServiceNames === null){
        return;
    }

    const pathDirYmls = getTemplateDirPath();
    fs.readdirSync(pathDirYmls).forEach(file => {
        const filePath = path.resolve(pathDirYmls, file);
        let fileChanghed = false;
        if (path.extname(file) === '.yml') {
            const doc = yaml.load(fs.readFileSync(filePath, 'utf8'));
            if (filterServiceNames) {
                Object.keys(doc.services).forEach(service => {
                    if (!filterServiceNames.includes(service)) {
                        delete doc.services[service];
                        fileChanghed = true;
                    }
                });
            }
            if (fileChanghed){
                fs.writeFileSync(filePath, yaml.dump(doc), 'utf8');
            }
        }
    });
}

function copyTemplatesToYml() {
    fs.readdirSync(getTemplateDirPath()).forEach(file => {
        if (file.endsWith('.yml.template') || file.endsWith('.env.template')) {
            const pathFile = path.resolve(getTemplateDirPath(), file);
            const newFile = file.replace('.template', '');
            const pathNewFile = path.resolve(getTemplateDirPath(), newFile);
            fs.copyFileSync(pathFile, pathNewFile);
        }
    });
}

function replaceEnvironmentVariables (stackNumber, domain, cypressCiBuildId, ciCommitRefSlug, urlAragorn){
    const STACK_NAME = `test-${stackNumber}`;
    fs.readdirSync(getTemplateDirPath()).forEach(file => {
        if (file.endsWith('.yml') || file.endsWith('.env')) {
            const filePath = path.resolve(getTemplateDirPath(), file);
            let content = fs.readFileSync(filePath, 'utf-8');
            content = content.replace(/\${STACK_NUMBER}/g, stackNumber);
            content = content.replace(/\${STACK_NAME}/g, STACK_NAME);
            content = content.replace(/\${DOMAIN}/g, domain);
            content = content.replace(/\${CYPRESS_CI_BUILD_ID}/g, cypressCiBuildId);
            content = content.replace(/\${CI_COMMIT_REF_SLUG}/g, ciCommitRefSlug);
            if (process.env.DEPLOY_ENVIRONMENT === 'local') {
                content = content.replace(/URL_ARAGORN=.*/, `URL_ARAGORN=${urlAragorn}`);
            }
            fs.writeFileSync(filePath, content);
        }
    });
}

function replaceServiceTagsInComposeFiles(serviceBranchs) {
    
    if(serviceBranchs === undefined || serviceBranchs === '' || serviceBranchs === null){
        return;
    }

    const SERVICE_BRANCHS_ARRAY = serviceBranchs.split(',');
    const serviceBranchsArray = SERVICE_BRANCHS_ARRAY.map(branch => {
        const [service, branchName] = branch.split('=');
        const normalizedBranchName = normalizeBranchName(branchName);
        return {service, branchName, normalizedBranchName };
    });
   
    const pathDirYmls = getTemplateDirPath();
    fs.readdirSync(pathDirYmls).forEach(file => {
        const filePath = path.resolve(pathDirYmls, file);
        let fileChanghed = false;
        if (path.extname(file) === '.yml') {
            const doc = yaml.load(fs.readFileSync(filePath, 'utf8'));

            serviceBranchsArray.forEach(service => {
            if (doc.services && doc.services[service.service]) {
                const image = doc.services[service.service].image;
                if (image) {
                    const parts = image.split(':');
                    parts[1] = service.normalizedBranchName;
                    doc.services[service.service].image = parts.join(':');
                    fileChanghed = true;
                }
            }
            });

            if (fileChanghed){
                fs.writeFileSync(filePath, yaml.dump(doc), 'utf8');
            }
        }
    });
}

function fixZwDatabaseMigration(stackName = 'test-1'){
    log('pacto:deploy',"Correção: remover sinalizados de atualização do banco do zw...");
    const postgresContainerIds = execSync(`docker ps -q --filter "name=${stackName}_postgres"`).toString().trim();
    postgresContainerIds.split('\n').forEach(containerId => {
        const result = execSync(`docker exec ${containerId} psql -U postgres -d bdzillyonteste -c "delete from sinalizadorsistema;"`).toString();
        log('pacto:deploy', 'Resultado da execução do comando:', result);
    });
};

async function migrateZw(baseUrlZw, chave) {
    log('pacto:deploy',"Migrando base de dados do zw");
    try {   
        const responseMigrateZw = await axios.post(`${baseUrlZw}/prest/versao?chave=${chave}`);
        log('pacto:deploy','Resposta do processo de migração = ', responseMigrateZw.data);   
    } catch (error) {
        console.error('Erro ao migrar base de dados do zw:', error.code, error.message, error.response.data);
        process.exit(1);
    }
}

async function migrateTreino(baseUrlTreino, chave) {
    log('pacto:deploy',"Migrando base de dados do treino...");
    try {   
        const responseMigrateTreino = await axios.post(`${baseUrlTreino}/prest/config/${chave}/updateBD`);
        log('pacto:deploy', 'Resposta do processo de migração =', responseMigrateTreino.data);
    } catch (error) {
        console.error('Erro ao migrar base de dados do treino:', error.code, error.message, error.response.data);
        process.exit(1);
    }
}

async function wait(url = '', name = '', params = {}, filterServiceNames = []) {
    if(filterServiceNames && filterServiceNames.length > 0 && !filterServiceNames.includes(name)){
        log('pacto:deploy', `Service ${name} not in filter list, skipping...`);
        return;
    }
    const verbose = process.env.DEBUG.includes('pacto:wait') || process.env.DEBUG.includes('pacto:deploy') || process.env.DEBUG.includes('pacto:*');

    if(params){
        let joinQueryToken = url.includes('?') ? '&' : '?';
        Object.keys(params).forEach(key => {
            url += `${joinQueryToken}${key}=${params[key]}`;
            joinQueryToken = '&';
        });
    }

    let resource = url.replace('http://', 'http-get://').replace('https://', 'https-get://');
    
    const opts = {
        resources: [resource],
        interval: 5000, 
        timeout: 360000,
        verbose
    };

    try {
        spinner.info(`Aguardando serviço ${name} em ${url}...`).start();
        const initTime = new Date().getTime();
        await waitOn(opts);
        const endTime = new Date().getTime();
        const milliseconds = endTime - initTime;
        const seconds = Math.floor(milliseconds / 1000);
        const time = seconds > 0 ? `${seconds}s` : `${milliseconds % 1000}ms`;
        log('pacto:deploy', `Serviço ${name} em ${url} está disponível após ${time}`);
    }catch(error){
        spinner.fail(`Erro ao aguardar o serviço ${name} em ${url}: ${error.message}`);
        process.exit(1);
    }
}

async function addExtraHostsFromEnvToComposeFiles (ip, deployEnvironment) {

    log('pacto:deploy',`O ip utilizado em extra_hosts é ${ip}`);
    const result = configDotenv({ path: path.resolve(getTemplateDirPath(), '.env')});

    if (result.error) {
        throw result.error;
    }

    const keys = Object.keys(result.parsed);

    const urlKeys = keys.filter(key => key.startsWith('URL_') && !(deployEnvironment === 'local' && key === 'URL_ARAGORN'));

    const domains = [...new Set(urlKeys.map(key => new url.URL(result.parsed[key]).hostname)
        .filter(domain => domain !== null && domain !== undefined && domain !== ''))];

    walkDir(getTemplateDirPath(), function(filePath) {
        if (filePath.endsWith('.yml') && !filePath.includes('proxy.yml') && !filePath.includes('cypress.build.yml')) {
            const doc = yaml.load(fs.readFileSync(filePath, 'utf8'));
    
            Object.keys(doc.services).forEach(service => {
                doc.services[service].extra_hosts = domains.map(domain => `${domain}:${ip}`);
            });
    
            fs.writeFileSync(filePath, yaml.dump(doc));
        }
    });

    log('pacto:deploy', 'Dominios encontrados no arquivo .env e configurados no host:', domains);
    if(isRoot()){
        addDomainsToHost(domains, ip, `Pacto Stack ${result.parsed.STACK_NAME}`);
    }else{
        execConfigHostCommand(domains, ip, `Pacto Stack ${result.parsed.STACK_NAME}`);
    }
}

function execConfigHostCommand(domains, ip) {
    const indexCommandFilePath = path.resolve(root(), 'index.js');
    try{
        const domainsParam = domains.join(',');
        const resultNetworkConfig = execSync(`node ${indexCommandFilePath} network hosts-pipe --domains ${domainsParam} --ip ${ip}`).toString();
        log('pacto:network', resultNetworkConfig);
    }catch(error){
        console.error('Erro ao executar comando de configuração de rede:',  error.message);
        process.exit(1);
    }
}

function getTemplateDirPath() {
    return path.resolve(root(), 'deploy/swarm/template');
}

function remove(stackName) {
    
    try {
        const result = execSync(`docker stack rm ${stackName}`).toString();
        log('pacto:deploy', result);
        while (true) {
            try {
                const outputCommand = execSync(`docker stack ls`).toString();
                const regexStackName = new RegExp(stackName, 'g');
                const ls = outputCommand.match(regexStackName);
                
                if (ls.includes(stackName)) {
                    sleep(300);
                }else{
                    break;
                }
            } catch (error) {
                break;
            }
        }
    } catch (error) {
        console.error(`Erro ao remover a stack ${stackName}: ${error}`);
        process.exit(1);
    }
}

module.exports = { deploy, getSwarmServiceGroups, getSwarmServiceGoupNames, getSwarmServicesGroupByName };