const { exec, spawn } = require('child_process');
const inquirer = require('inquirer');
const autocomplete = require('inquirer-autocomplete-prompt');
inquirer.registerPrompt('autocomplete', autocomplete);
const ora = require('ora');
const { normalizeBranchName } = require('../git/branch');
const { log } = require('../util/log');
const { getDockerTagsForService, getGitlabToken } = require('../git/gitlab');

async function getRunningServices() {
    return new Promise((resolve, reject) => {
        exec('docker ps --filter "name=dev-tools" --format "{{.Names}}"', (err, stdout, stderr) => {
            if (err) {
                reject(`Erro ao listar os serviços em execução: ${err}`);
                return;
            }
            const services = stdout.split('\n').filter(name => name);
            resolve(services);
        });
    });
}


module.exports = { changeBranchCommand };