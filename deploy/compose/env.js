const fs = require('fs');
const { exec } = require('child_process');
const path = require('path');

function geComposeUrlsFromEnv() {
    const composeFilePath = path.resolve(__dirname, 'docker-compose.yml');
    const composeFile = fs.readFileSync(composeFilePath, 'utf8');
    const urlLines = composeFile.split('\n').filter(line => line.includes('URL'));
    const urls = urlLines.map(line => {
        const [key, value] = line.split('=');
        return { name: key.trim(), value: value.trim() };
    });
    return urls;
}

function getUrlsFromDockerCompose() {
    const composeFilePath = path.resolve(__dirname, 'docker-compose.yml');
    const composeFile = fs.readFileSync(composeFilePath, 'utf8');
    const urlLines = composeFile.split('\n').filter(line => line.includes('URL'));
    const urls = urlLines.map(line => {
        const colonIndex = line.indexOf(':');
        const key = line.substring(0, colonIndex).trim();
        const value = line.substring(colonIndex + 1).trim();
        return { name: key.trim(), value: value.trim() };
    });
    return urls;
}

function getUrlsFromPostgresContainer() {
    return new Promise((resolve, reject) => {
        exec('docker ps --filter "ancestor=registry.gitlab.com/plataformazw/docker-pacto/postgres:9.4" --format "{{.ID}}"', (err, stdout, stderr) => {
            if (err) {
                console.error(`Erro ao encontrar o container do PostgreSQL: ${err.message}`);
                reject(err);
                return;
            }
            const containerId = stdout.trim();
            if (!containerId) {
                reject(new Error('Nenhum container do PostgreSQL encontrado.'));
                return;
            }

            exec(`docker exec ${containerId} printenv`, (err, stdout, stderr) => {
                if (err) {
                    console.error(`Erro ao inspecionar o container do PostgreSQL: ${err.message}`);
                    reject(err);
                    return;
                }
                const envVars = stdout.split('\n');
                const urls = envVars
                    .filter(env => {
                        return env.includes('URL') && 
                        !env.includes('TOKEN_CURL_DOWNLOAD') &&
                        !env.includes('URL_DYNAMODB') &&
                        !env.includes('USAR_URL_RECURSO_EMPRESA');
                    })
                    .map(env => {
                        const [key, value] = env.split('=');
                        return { name: key, value: value };
                    });
                if (urls.length > 0) {
                    resolve(urls);
                } else {
                    reject(new Error('Nenhuma variável de ambiente contendo URL encontrada.'));
                }
            });
        });
    });
}

module.exports = { geComposeUrlsFromEnv, getUrlsFromPostgresContainer, getUrlsFromDockerCompose };