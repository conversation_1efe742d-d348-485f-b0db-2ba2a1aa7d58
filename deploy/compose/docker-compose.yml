x-common-environments: &a1
  ENVIRONMENT: dev
  PROFILE: dev
  SERVER_PORT: 8080
  DEBUG: "true"
  IP_HOST: host.docker.internal
  MENCACHE_HOST: host.docker.internal:11211
  BRANCH_BD_TESTE: hotfix/ia-300
  POSTGRES_API_URL: http://host.docker.internal:8432
  CONTEXTO: ""
  AMAZON_DYNAMODB_ACCESSKEY: AMAZON_DYNAMODB_ACCESSKEY
  AMAZON_DYNAMODB_REGION: ""
  AMAZON_DYNAMODB_SECRETKEY: wzkere
  ENABLE_CAPTCHA: "false"
  ENABLE_CAPTCHA_USER_OAMD: "false"
  URL_DYNAMODB: http://host.docker.internal:8801
  URL_DYNAMODB_ADMIN: http://host.docker.internal:8802
  DYNAMO_ENDPOINT: http://host.docker.internal:8801
  HEALTHCHECK_KEY: teste
  INIT_DB: teste
  UPLOAD_TO_NUVEM: "false"
  OAMD_URL: ************************************
  BUSCAR_CONHECIMENTO_UCP: "false"
  MAX_GPT: "false"
  REDIS_REDISINSIGHT_URL: http://localhost:5540
  JAEGER_URL: http://host.docker.internal:16686
  NGROK_CONVERSAS_AI_URL: https://sincere-officially-crayfish.ngrok-free.app
  USAR_URL_RECURSO_EMPRESA: "true"
  CONVERSAS_IA_KEY: d6403cd04e614da3adcade848c68146b10266f34f40abac57e064d10ccd1055f
  AUTENTICACAO_MS_MASTER_KEY: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9
  URL_NOVO_TREINO: http://host.docker.internal:8000
  URL_VENDAS_2: http://host.docker.internal:8001
  URL_VENDAS_ONLINE: http://host.docker.internal:8001
  URL_HTTP_PLATAFORMA_PACTO: http://host.docker.internal:8002
  URL_ADM: http://host.docker.internal:8003
  URL_ZW_FRONT: http://host.docker.internal:8003
  URL_NOVO_LOGIN: http://host.docker.internal:8004
  URL_AUTENTICACAO: http://host.docker.internal:8100
  DISCOVERY_URL: http://host.docker.internal:8101
  URL_ARAGORN: http://1421652907-aragorn.pipe-zw.pactosolucoes.com.br
  URL_GRADUACAO: http://host.docker.internal:8103
  URL_RELATORIO_FULL: http://host.docker.internal:8104
  URL_CLIENTE_MS: http://host.docker.internal:8105
  URL_BI_MS: http://host.docker.internal:8106
  URL_MEDIA_MS: http://host.docker.internal:8107
  URL_CAD_AUX_MS: http://host.docker.internal:8109
  URL_CONTATO_MS: http://host.docker.internal:8110/crm-ms
  URL_CRM_MS: http://host.docker.internal:8110/crm-ms
  URL_ADM_CORE_MS: http://host.docker.internal:8111
  URL_PERSONAGEM: http://host.docker.internal:8112
  URL_PESSOA_MS: http://host.docker.internal:8113
  URL_PACTOPAY_DASH: http://host.docker.internal:8114
  URL_PLANO_MS: http://host.docker.internal:8115
  URL_PRODUTO_MS: http://host.docker.internal:8116
  URL_PACTOPAY_MS: http://host.docker.internal:8117
  URL_ACESSO_SISTEMA_MS: http://host.docker.internal:8118
  URL_ADM_MS: http://host.docker.internal:8119/adm-ms
  URL_MIDIASOCIAL_MS: http://host.docker.internal:8120
  URL_MAKETING_MS: http://host.docker.internal:8121
  URL_MENU_MS: http://host.docker.internal:8122
  URL_NOTIFICACAO_MS: http://host.docker.internal:8123
  URL_FINANCEIRO_MS: http://host.docker.internal:8124/financeiro-ms
  URL_RECURSO_MS: http://host.docker.internal:8125/recurso-ms
  URL_RELATORIO_MS: http://host.docker.internal:8126
  URL_NICHOS: http://host.docker.internal:8007
  URL_ZW: http://host.docker.internal:8200/ZillyonWeb
  URL_TREINO: http://host.docker.internal:8201/TreinoWeb
  URL_OAMD: http://host.docker.internal:8202/NewOAMD
  URL_ZW_API: http://host.docker.internal:8203/API-ZillyonWeb/prest
  URL_API: http://host.docker.internal:8203/API-ZillyonWeb/prest
  URL_LOGIN: http://host.docker.internal:8204/LoginApp
  URL_JENKINS: http://host.docker.internal:8205
  URL_GAME: http://host.docker.internal:8206/GameofResults
  URL_RESTORE_DB: http://host.docker.internal:8432/restore/chave
  URL_PACTO_CONVERSAS: http://host.docker.internal:8300
  URL_API_GATEWAY: http://host.docker.internal:8205
  URL_API_DOC: http://host.docker.internal:8207
services:
  treino-front:
    image: registry.gitlab.com/plataformazw/zw_ui/novo-treino:master
    environment:
      <<: *a1
    extra_hosts:
      - host.docker.internal:host-gateway
    restart: on-failure
    ports:
      - 8000:80
    labels:
      - projectName=zw_ui
  vendas-online-front:
    image: registry.gitlab.com/plataformazw/vendasonline-v2.0:master
    extra_hosts:
      - host.docker.internal:host-gateway
    environment:
      <<: *a1
      URL_API: http://host.docker.internal:8203/API-ZillyonWeb/prest
    restart: on-failure
    ports:
      - 8001:80
  login-front:
    image: registry.gitlab.com/plataformazw/zw_ui/login:master
    restart: on-failure
    extra_hosts:
      - host.docker.internal:host-gateway
    environment:
      <<: *a1
    ports:
      - 8004:80
  marketing-front:
    image: registry.gitlab.com/plataformazw/zw_ui/marketing:master
    restart: on-failure
    extra_hosts:
      - host.docker.internal:host-gateway
    environment:
      <<: *a1
    ports:
      - 8006:80
  nichos-front:
    image: registry.gitlab.com/plataformazw/zw_ui/nichos:master
    restart: on-failure
    extra_hosts:
      - host.docker.internal:host-gateway
    environment:
      <<: *a1
    ports:
      - 8007:80
  autenticacao-ms:
    image: registry.gitlab.com/plataformazw/autenticacao:master
    environment:
      <<: *a1
    ports:
      - 8100:8080
      - 9100:9000
    depends_on:
      - postgres
    restart: on-failure
    extra_hosts:
      - host.docker.internal:host-gateway
  discovery-ms:
    image: registry.gitlab.com/plataformazw/discovery-urls:master
    environment:
      <<: *a1
    ports:
      - 8101:8080
      - 9101:9000
    depends_on:
      - postgres
    restart: on-failure
    extra_hosts:
      - host.docker.internal:host-gateway
  graduacao-ms:
    image: registry.gitlab.com/plataformazw/graduacao-ms:master
    environment:
      <<: *a1
    ports:
      - 8103:8080
      - 9103:9000
    depends_on:
      - postgres
    restart: on-failure
    extra_hosts:
      - host.docker.internal:host-gateway
  relatorio-full-ms:
    image: registry.gitlab.com/plataformazw/relatorio-full:master
    environment:
      <<: *a1
    ports:
      - 8104:8080
      - 9104:9000
    restart: on-failure
    extra_hosts:
      - host.docker.internal:host-gateway
  cliente-ms:
    image: registry.gitlab.com/plataformazw/cliente-ms:master
    ports:
      - 8105:8080
      - 9105:9000
    environment:
      <<: *a1
    restart: on-failure
    extra_hosts:
      - host.docker.internal:host-gateway
  bi-ms:
    image: registry.gitlab.com/plataformazw/bi-ms:master
    environment:
      <<: *a1
    ports:
      - 8106:8080
      - 9106:9000
    restart: on-failure
    extra_hosts:
      - host.docker.internal:host-gateway
  media-ms:
    image: registry.gitlab.com/plataformazw/media-ms:master
    ports:
      - 8107:8080
      - 9107:9000
    environment:
      <<: *a1
    restart: on-failure
    extra_hosts:
      - host.docker.internal:host-gateway
  leads-ms:
    image: registry.gitlab.com/plataformazw/leads-via-email:master
    ports:
      - 8108:8080
      - 9108:9000
    restart: on-failure
    environment:
      <<: *a1
      API_KEY: vMifdNfYBomwFGsYfHWjCUnXPdHzKupPKdwG
      API_URL: http://host.docker.internal:8203/API-ZillyonWeb
    extra_hosts:
      - host.docker.internal:host-gateway
  cad-aux-ms:
    image: registry.gitlab.com/plataformazw/cad-aux-ms:master
    environment:
      <<: *a1
    restart: on-failure
    ports:
      - 8109:8080
      - 9109:9000
    depends_on:
      - postgres
    extra_hosts:
      - host.docker.internal:host-gateway
  crm-ms:
    image: registry.gitlab.com/plataformazw/flash/crm-ms:master
    environment:
      <<: *a1
    restart: on-failure
    ports:
      - 8110:8080
      - 9110:9000
    depends_on:
      - postgres
      - memcached
    extra_hosts:
      - host.docker.internal:host-gateway
  adm-core-ms:
    image: registry.gitlab.com/plataformazw/adm-core-ms:master
    environment:
      <<: *a1
    restart: on-failure
    ports:
      - 8111:8080
      - 9111:9000
    depends_on:
      - postgres
    extra_hosts:
      - host.docker.internal:host-gateway
  personagem-ms:
    image: registry.gitlab.com/plataformazw/personagem:master
    environment:
      <<: *a1
    restart: on-failure
    ports:
      - 8112:8080
      - 9112:9000
    extra_hosts:
      - host.docker.internal:host-gateway
  pessoa-ms:
    image: registry.gitlab.com/plataformazw/pessoa-ms:master
    environment:
      <<: *a1
    restart: on-failure
    ports:
      - 8113:8080
      - 9113:9000
    depends_on:
      - postgres
    extra_hosts:
      - host.docker.internal:host-gateway
  pactopay-dash-ms:
    image: registry.gitlab.com/pactopay/payments/dash-api:master
    environment:
      <<: *a1
    ports:
      - 8114:8080
      - 9114:9000
    restart: on-failure
    depends_on:
      - postgres
    extra_hosts:
      - host.docker.internal:host-gateway
  plano-ms:
    image: registry.gitlab.com/plataformazw/plano-java-ms:master
    restart: on-failure
    environment:
      <<: *a1
    ports:
      - 8115:8080
      - 9115:9000
    depends_on:
      - postgres
    extra_hosts:
      - host.docker.internal:host-gateway
  produto-ms:
    image: registry.gitlab.com/plataformazw/produto-ms:master
    environment:
      <<: *a1
    restart: on-failure
    ports:
      - 8116:8080
      - 9116:9000
    depends_on:
      - postgres
    extra_hosts:
      - host.docker.internal:host-gateway
  pactopay-ms:
    image: registry.gitlab.com/pactopay/pactopay-ms:master
    restart: on-failure
    environment:
      <<: *a1
    ports:
      - 8117:8080
      - 9117:9000
    depends_on:
      - postgres
    extra_hosts:
      - host.docker.internal:host-gateway
  acesso-sistema-ms:
    image: registry.gitlab.com/plataformazw/acesso-sistema-ms:master
    restart: on-failure
    environment:
      <<: *a1
    ports:
      - 8118:8080
      - 9118:9000
    depends_on:
      - postgres
    extra_hosts:
      - host.docker.internal:host-gateway
  adm-ms:
    image: registry.gitlab.com/plataformazw/adm-ms:master
    environment:
      <<: *a1
      PROFILE: hml
    restart: on-failure
    ports:
      - 8119:8080
      - 9119:9000
    depends_on:
      - postgres
    extra_hosts:
      - host.docker.internal:host-gateway
  midia-social-ms:
    image: registry.gitlab.com/plataformazw/migracao/midia-social-ms:master
    restart: on-failure
    environment:
      <<: *a1
    ports:
      - 8120:8080
      - 9120:9000
    extra_hosts:
      - host.docker.internal:host-gateway
  marketing-ms:
    image: registry.gitlab.com/plataformazw/marketing-ms:main
    restart: on-failure
    environment:
      <<: *a1
    ports:
      - 8121:8080
      - 9121:9000
    extra_hosts:
      - host.docker.internal:host-gateway
  menu-ms:
    image: registry.gitlab.com/plataformazw/marketing-ms:main
    restart: on-failure
    environment:
      <<: *a1
    ports:
      - 8122:8080
      - 9122:9000
    extra_hosts:
      - host.docker.internal:host-gateway
  notificacao-ms:
    image: registry.gitlab.com/plataformazw/notificacao:master
    restart: on-failure
    environment:
      <<: *a1
    ports:
      - 8123:8080
      - 9123:9000
    extra_hosts:
      - host.docker.internal:host-gateway
  financeiro-ms:
    image: registry.gitlab.com/plataformazw/financeiro-ms:master
    restart: on-failure
    environment:
      <<: *a1
    ports:
      - 8124:8080
      - 9124:9000
    extra_hosts:
      - host.docker.internal:host-gateway
  recurso-ms:
    image: registry.gitlab.com/plataformazw/recurso-ms:master
    restart: on-failure
    environment:
      <<: *a1
    ports:
      - 8125:8080
      - 9125:9000
    extra_hosts:
      - host.docker.internal:host-gateway
  relatorio-ms:
    image: registry.gitlab.com/plataformazw/relatorio-ms:master
    restart: on-failure
    environment:
      <<: *a1
    ports:
      - 8126:8080
      - 9126:9000
    extra_hosts:
      - host.docker.internal:host-gateway
  clube-vantagens-ms:
    image: registry.gitlab.com/plataformazw/clube-vantagens-ms:master
    restart: on-failure
    environment:
      <<: *a1
    ports:
      - 8127:8080
      - 9127:9000
    extra_hosts:
      - host.docker.internal:host-gateway
  zw:
    image: registry.gitlab.com/plataformazw/zw/tomcat:master
    environment:
      <<: *a1
      SMTP_EMAIL_ROBO: <EMAIL>
      SMTP_EMAIL_NOREPLY: <EMAIL>
      SMTP_LOGIN_ROBO: <EMAIL>
      SMTP_SENHA_ROBO: **************************************************
      SMTP_SERVER_ROBO: smtp.mailgun.org
      SMTP_SERVER_ROBO_INICIAR_TLS: "true"
      SMTP_SERVER_ROBO_CONEXAO_SEGURA: "false"
      URL_MAILING: http://host.docker.internal:8200/ZillyonWeb/ms
      URL_JENKINS: http://host.docker.internal:8205
      ENABLE_MENU_ZW_UI: "true"
      HABILITAR_NICHO: "false"
      HABILITAR_CACHE_INIT_NICHO: "false"
      VALIDADE_CACHE_NICHO_EM_MINUTOS: 0
      ENABLE_OTEL_JAVA_AGENT: "false"
      OTEL_LOG_LEVEL: debug
      OTEL_LOGS_EXPORTER: console
      OTEL_METRICS_EXPORTER: none
      TIPO_MIDIA: AWS_S3
      URL_FOTOS_NUVEM: https://cdn1.pactorian.net
      VALIDAR_TOKEN_API_ZW: "true"
      MOCK_ARAGORN_CARDS: "true"
    ports:
      - 8200:8080
      - 9200:9000
    depends_on:
      - postgres
      - memcached
    restart: on-failure
    extra_hosts:
      - 1421652907-aragorn.pipe-zw.pactosolucoes.com.br:**********
      - host.docker.internal:host-gateway
  treino:
    image: registry.gitlab.com/plataformazw/treino/tomcat:develop
    environment:
      <<: *a1
      URL_ZW_INTEGRACAO: http://host.docker.internal:8200/ZillyonWeb
    ports:
      - 8201:8080
      - 9201:9000
    depends_on:
      - postgres
    restart: on-failure
    extra_hosts:
      - host.docker.internal:host-gateway
  oamd:
    image: registry.gitlab.com/plataformazw/oamd/tomcat:master
    environment:
      DEBUG: "true"
    ports:
      - 8202:8080
      - 9202:9000
    depends_on:
      - postgres
    restart: on-failure
    extra_hosts:
      - host.docker.internal:host-gateway
  api:
    image: registry.gitlab.com/plataformazw/api:master
    environment:
      <<: *a1
    ports:
      - 8203:8080
      - 9203:9000
    restart: on-failure
    extra_hosts:
      - host.docker.internal:host-gateway
  login:
    image: registry.gitlab.com/plataformazw/login/tomcat:master
    environment:
      <<: *a1
      URL_DATABASE_OAMD: host.docker.internal
    ports:
      - 8204:8080
      - 9204:9000
    restart: on-failure
    depends_on:
      - postgres
      - dynamodb
    extra_hosts:
      - host.docker.internal:host-gateway
  game:
    image: registry.gitlab.com/plataformazw/gor/tomcat:master
    ports:
      - 8206:8080
    environment:
      <<: *a1
    extra_hosts:
      - host.docker.internal:host-gateway
  api-gateway:
    image: registry.gitlab.com/plataformazw/api-gateway:master
    ports:
      - 8205:8080
      - 9205:9000
    environment:
      <<: *a1
    extra_hosts:
      - host.docker.internal:host-gateway
  api-doc:
    image: registry.gitlab.com/plataformazw/pacto-api-doc:main
    ports:
      - 8207:80
    environment:
      <<: *a1
    extra_hosts:
      - host.docker.internal:host-gateway
  conversas-ai-api:
    image: registry.gitlab.com/plataformazw/ia/orion/api:bugfix-ia-1241
    ports:
      - 8300:8080
      - 5677:5677
    labels:
      - projectName=orion
    environment:
      SERVER_PORT: 8080
      REDIS_URL: redis://redis:6379/0
      FLASK_ENV: development
      WAITRESS_THREADS: 4
      API_MASTER_KEY: 5eea8f6d-8001-4165-ad00-e1c8543b520b3e68fa15-886c-4491-b057-4b27b17df562
      AUTH_ON: "false"
      GOOGLE_LOGIN_DOC: false
      SEPARATE_API_KEY: false
      DOMAIN: http://localhost:8300
      GLOBAL_RATE_LIMIT: 60/minute
      MODE: api
      RETRY_CONTACT_TIME: 1
      ALLOWED_ORIGINS: https://api.z-api.io, http://localhost:8300, ************
      Z_API_CLIENT_TOKEN: F3b14fd459eb9462ebd51b359510e2c4dS
      RETRY_MESSAGE: true
      GCP_BIGQUERY_DATASET: development
      GCP_BIGQUERY_PROJECT_ID: conversas-ai
      URL_PACTO_DISCOVERY_MS: http://host.docker.internal:8101
    healthcheck:
      test:
        - CMD
        - curl
        - -f
        - http://localhost:8080/health/
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    extra_hosts:
      - host.docker.internal:host-gateway
    depends_on:
      - redis
  conversas-ai-worker:
    image: registry.gitlab.com/plataformazw/ia/orion/worker:main
    ports:
      - 8301:8080
      - 5678:5678
      - 5686:5686
    labels:
      - projectName=orion
    environment:
      REDIS_URL: redis://redis:6379/0
      FLASK_ENV: development
      NUM_THREADS: 4
      BUFFER_SIZE: 30
      MODE: worker
      Z_API_INSTANCE_STATUS_CHECK_INTERVAL: 2
      Z_API_INSTANCE_STATUS_CHECK: false
      Z_API_CLIENT_TOKEN: F3b14fd459eb9462ebd51b359510e2c4dS
      BUCKET_NAME_AUDIO: temporary_audios
      HEALTHCHECK_PORT: 8080
      PENDENCY_VERIFICATION_TIME: 5
      CHECK_PENDENCY: true
      DOMAIN: http://localhost:8300
      ROLES_TO_KEEP_REDIS: assistant, user, system
      SECONDS_TO_WAIT_TILL_RESPONSE: 5
      RETRY_CONTACT_TIME: 10
      LINK_APP_TREINO: https://apptreino.com.br/#baixar
      GCP_BIGQUERY_DATASET: development
      GCP_BIGQUERY_PROJECT_ID: conversas-ai
      URL_PACTO_DISCOVERY_MS: http://host.docker.internal:8101
      DELAYED_QUEUE_MAX_WORKERS: 10
    healthcheck:
      test:
        - CMD
        - curl
        - -f
        - http://localhost:8080/health_check
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    extra_hosts:
      - host.docker.internal:host-gateway
    depends_on:
      - redis
  conversas-ai-scheduler:
    image: registry.gitlab.com/plataformazw/ia/orion/scheduler:main
    ports:
      - 8302:8080
    labels:
      - projectName=orion
    environment:
      REDIS_URL: redis://redis:6379/0
      FLASK_ENV: development
      MODE: worker
      RQ_QUEUE_NAME: scheduler_queue
      HEALTHCHECK_PORT: 8080
      DOMAIN: http://localhost:8300
      GCP_BIGQUERY_PROJECT_ID: conversas-ai
      GCP_BIGQUERY_DATASET: development
    healthcheck:
      test:
        - CMD
        - curl
        - -f
        - http://localhost:8080/health_check
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    extra_hosts:
      - host.docker.internal:host-gateway
    depends_on:
      - redis
  conversas-ai-docs-worker:
    image: registry.gitlab.com/plataformazw/ia/orion/docs_worker:main
    ports:
      - 8303:8084
    environment:
      TZ: America/Sao_Paulo
      SMTP_USERNAME: <EMAIL>
      SMTP_PASSWORD: a99162af848595da80cdecf7ca9b1ca2-0920befd-9ec2e0a9
      REDIS_URL: redis://redis:6379/0
      FLASK_ENV: development
      HEALTHCHECK_PORT: 8083
      MODE: docs_worker
    healthcheck:
      test:
        - CMD
        - curl
        - -f
        - http://localhost:8083/health_check
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    depends_on:
      - redis
      - qdrant
  conversas-link-backend:
    image: registry.gitlab.com/plataformazw/ia/conversa-link/api:main
    ports:
      - 8304:3333
    depends_on:
      - conversas-link-frontend
    environment:
      - NODE_ENV=development
      - DISCOVERY_URL=http://host.docker.internal:8101
      - USUARIO=pactobr
      - SENHA=123
      - PORT=3333
  conversas-link-frontend:
    image: registry.gitlab.com/plataformazw/ia/conversa-link/front:main
    ports:
      - 8305:80
    environment:
      - NODE_ENV=development
      - BACKEND_URL=http://host.docker.internal:8304
  ngrok:
    image: ngrok/ngrok:latest
    command: start --all --config=/ngrok.yml
    volumes:
      - ./ngrok.yml:/ngrok.yml
    ports:
      - 4040:4040
    extra_hosts:
      - host.docker.internal:host-gateway
  redis:
    image: redis
    ports:
      - 6379:6379
    depends_on:
      - redisinsight
  redisinsight:
    image: redislabs/redisinsight:latest
    ports:
      - 5540:5540
    environment:
      - REDISINSIGHT_DEFAULT_CONNECTIONS=[{"name":"Local
        Redis","host":"redis","port":6379}]
  qdrant:
    image: qdrant/qdrant:latest
    ports:
      - 6333:6333
  jenkins:
    image: registry.gitlab.com/plataformazw/docker-pacto/jenkins:2.184
    restart: on-failure
    depends_on:
      - memcached
    ports:
      - 8205:8080
      - 50000:50000
    extra_hosts:
      - host.docker.internal:host-gateway
  postgres:
    image: registry.gitlab.com/plataformazw/docker-pacto/postgres:9.4
    environment:
      <<: *a1
      BDZILLYON_VERSION: 2178
      POSTGRES_MAX_CONNECTIONS: 1000
    restart: always
    ports:
      - 5432:5432
      - 8432:8080
    extra_hosts:
      - host.docker.internal:host-gateway
    command:
      - postgres
      - -c
      - log_statement=all
      - -c
      - max_connections=2000
  dynamodb:
    image: registry.gitlab.com/plataformazw/docker-pacto/dynanmodb:1.22.0-master
    restart: always
    ports:
      - 8801:8000
  dynamodb-admin:
    image: aaronshaf/dynamodb-admin
    ports:
      - 8802:8001
    environment:
      <<: *a1
    extra_hosts:
      - host.docker.internal:host-gateway
  memcached:
    image: memcached:1.5.6-alpine
    restart: always
    ports:
      - 11211:11211
  rabbitmq:
    image: rabbitmq:3.8.9-management
    restart: always
    ports:
      - 5672:5672
      - 15672:15672
    volumes:
      - ./rabbitmq-data:/var/lib/rabbitmq
    environment:
      RABBITMQ_DEFAULT_USER: admin
      RABBITMQ_DEFAULT_PASS: vacacomgripe
    extra_hosts:
      - host.docker.internal:host-gateway
  jaeger:
    image: jaegertracing/all-in-one:1.39
    ports:
      - 16686:16686
      - 4317:4317
    environment:
      COLLECTOR_OTLP_ENABLED: "true"
    extra_hosts:
      - host.docker.internal:host-gateway
