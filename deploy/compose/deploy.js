const { exec, execSync } = require('child_process');
const path = require('path');
const fs = require('fs');
const chalk = require('chalk');
const { isRunningInContainer } = require('../../util/container');
const { logSuccess, logError, logWarning, logInfo, logDeploy } = require('../../util/log.js');
const { resetServiceTagsToDefault } = require('./services.js');

function getComposePath() {
    return path.resolve(__dirname, 'docker-compose.yml');
}

function isHostGatewaySupported() {
    try {
        // Tenta executar um comando docker que usa host-gateway para ver se é suportado
        execSync('docker run --rm --add-host=host.docker.internal:host-gateway alpine ping -c 1 host.docker.internal', { stdio: 'ignore' });
        return true;
    } catch (error) {
        return false;
    }
}

function replaceHostGateway() {
    const composeFilePath = getComposePath();
    
    try {
        logInfo('🔄 Substituindo host-gateway por ********** no arquivo docker-compose.yml...');

        // Ler o conteúdo do arquivo
        let content = fs.readFileSync(composeFilePath, 'utf8');

        // Substituir todas as ocorrências de host-gateway por **********
        content = content.replace(/host-gateway/g, '**********');

        // Escrever o conteúdo modificado de volta no arquivo
        fs.writeFileSync(composeFilePath, content, 'utf8');

        logSuccess('✅ Substituição concluída com sucesso!');
    } catch (error) {
        logError(`❌ Erro ao substituir host-gateway: ${error.message}`);
        throw error;
    }
}

async function deployCompose(selectedServices) {
    const composeFilePath = getComposePath();

    if (!isHostGatewaySupported()) {
        logWarning('⚠️ host-gateway não é suportado neste ambiente. Substituindo por **********...');
        replaceHostGateway();
    }

    return new Promise((resolve, reject) => {
        const composeCommand = getDockerComposeCommand();
        const process = exec(`${composeCommand} -p dev-tools -f "${composeFilePath}" up -d ${selectedServices}`);

        process.stdout.on('data', (data) => {
            logInfo(`${data}`);
        });

        process.stderr.on('data', (data) => {
            logError(`${data}`);
        });

        process.on('close', (code) => {
            if (code !== 0) {
                reject(new Error(`Process exited with code ${code}`));
            } else {
                logDeploy('🚀 Deploy realizado com sucesso!');
                logInfo('⏳ Aguarde a inicialização dos serviços...');

                if(!isRunningInContainer()) {
                    exec('pacto n hd', (err, stdout, stderr) => {
                        if (err) {
                            logError(err);
                            reject(new Error(`Error: ${err.message}`));
                            return;
                        }else{
                            resolve();
                        }

                        logInfo(stdout);
                    });
                   
                }else{
                    resolve();
                }
            }
        });
    });
}

function resetBranchs() {
    try {
        const composeFilePath = getComposePath();
        const composeCommand = getDockerComposeCommand();

        logInfo('🔍 Verificando serviços em execução...');
        const runningServicesOutput = execSync(
            `${composeCommand} -p dev-tools -f "${composeFilePath}" ps --services`,
            { encoding: 'utf-8' }
        ).trim().replace(/\n/g, ' ');

        logInfo('🏷️ Resetando tags dos serviços para as tags padrão definidas nas labels...');
        const servicesUpdated = resetServiceTagsToDefault();
        if (servicesUpdated > 0) {
            logSuccess('📄 Tags dos serviços resetadas com sucesso para as tags padrão!');
        }

        if (runningServicesOutput) {
            logInfo('🔄 Reiniciando os serviços que estavam em execução...');
            execSync(`${composeCommand} -p dev-tools -f "${composeFilePath}" up -d ${runningServicesOutput}`, { stdio: 'inherit' });
            logSuccess('🎉 Serviços reiniciados com sucesso!');
        }

    } catch (error) {
        logError('❌ Erro ao resetar as branchs e reiniciar os serviços:', error.message);
    }
}


function getDockerComposeCommand() {
    try {
      execSync('docker compose version', { stdio: 'ignore' });
      return 'docker compose';
    } catch (error) {
      try {
        execSync('docker-compose version');
        return 'docker-compose';
      } catch (error) {
        throw new Error('Nenhum dos comandos docker compose ou docker-compose estão disponíveis');
      }
    }
  }


module.exports = { deployCompose, resetBranchs, getDockerComposeCommand, getComposePath };
