const { exec, execSync } = require('child_process');
const inquirer = require('inquirer');
const { log } = require('../util/log.js');
const autocomplete = require('inquirer-autocomplete-prompt');
inquirer.registerPrompt('autocomplete', autocomplete);
const ora = require('ora');
const spinner = ora();


async function publishDebugPort() {

    return new Promise((resolve, reject) => {
        exec('docker service ls --format "{{.Name}}"', (error, stdout, stderr) => {
            if (error) {
                const msg = `Erro ao obter serviços: ${error}`;
                reject(msg);
            }

            const services = stdout.split('\n').filter(Boolean);

            inquirer.prompt([
                {
                    type: 'autocomplete',
                    name: 'service',
                    message: 'Qual serviço você deseja debugar?',
                    source: function (answersSoFar, input) {
                        input = input || '';
                        return new Promise(function (resolveAutoComplete) {
                            const results = services.filter(service => service.includes(input));
                            resolveAutoComplete(results);
                        });
                    }
                }
            ])
                .then((answers) => {
                    const service = answers.service;

                    const serviceName = service.split('_')[1];
                    const debugPort = getDebugPort(serviceName);
                    try {
                        spinner.start(`Atualizando serviço ${service} para debug na porta ${debugPort}...`);
                        const resultUpdateService = execSync(`docker service update ${service} --force --env-add DEBUG=true --publish-add ${debugPort}:9000`);
                        const msg = `Serviço ${service} atualizado com sucesso para debug`;
                        spinner.succeed(msg);
                        log('pacto:deploy', msg);

                        resolve(msg);
                    } catch (error) {
                        const msg = `Erro ao atualizar o serviço: ${error}`;
                        reject(msg);
                    }
                });
        });
    });
}


function getDebugPort(service) {
    const defaultPorts = getDefaultPortsByService();
    const serviceNameDefaultPort = Object.keys(defaultPorts).find(key => key === service);
    return defaultPorts[serviceNameDefaultPort] || getRandomPort();
}

function getRandomPort() {
    return Math.floor(Math.random() * 10000) + 9000;
}

function getDefaultPortsByService(){
    return {
        "autenticacao-ms": 9100,
        "discovery-ms": 9101,
        "aragorn-ms": 9102,
        "graduacao-ms": 9103,
        "relatorio-full-ms": 9104,
        "cliente-ms": 9105,
        "bi-ms": 9106,
        "media-ms": 9107,
        "leads-ms": 9108,
        "cad-aux-ms": 9109,
        "crm-ms": 9110,
        "adm-core-ms": 9111,
        "personagem-ms": 9112,
        "pessoa-ms": 9113,
        "pactopay-dash-ms": 9114,
        "plano-ms": 9115,
        "produto-ms": 9116,
        "pactopay-ms": 9117,
        "acesso-sistema-ms": 9118,
        "adm-ms": 9119,
        "midia-social-ms": 9120,
        "marketing-ms": 9121,
        "menu-ms": 9122,
        "notificacao-ms": 9123,
        "financeiro-ms": 9124,
        "recurso-ms": 9125,
        "relatorio-ms": 9126,
        "clube-vantagens-ms": 9127,
        "zw": 9200,
        "treino": 9201,
        "oamd": 9202,
        "api": 9203,
        "login": 9204
    };
}

module.exports = { publishDebugPort };