const { exec } = require('child_process');
const waitOn = require('wait-on');
const chalk = require('chalk');
const { logSuccess, logError, logInfo, logWarning } = require('../util/log');

async function getRunningContainers() {
  return new Promise((resolve, reject) => {
    exec('docker ps --filter "status=running" --filter "name=dev-tools-" --format "{{.ID}}|{{.Names}}|{{.Image}}|{{.Status}}"', (err, stdout, stderr) => {
      if (err) {
        reject(`Erro ao listar os containers em execução: ${err}`);
        return;
      }
      const containers = stdout.split('\n').filter(line => line).map(line => {
        const [id, name, image, status] = line.split('|');
        return { id, name, image, status };
      });
      resolve(containers);
    });
  });
}

async function awaitZw() {
  const zwContainer = await findZwContainer();
  if (zwContainer) {
    logInfo('⏳ Aguardando o ZW iniciar...');
    const urlZw = await getContainerEnvVariable(zwContainer, "URL_ZW");
    await healthCheck(urlZw + "/prest/health");
    logSuccess('✅ ZW pronto!')
  }
}

async function awaitTreino() {
  const containerTreinoName = await findTreinoContainer();
  if (containerTreinoName) {
    logInfo('⏳ Aguardando o Treino iniciar...');
    const urlTreino = await getContainerEnvVariable(
      containerTreinoName,
      "URL_TREINO",
    );
    await healthCheck(urlTreino + "/prest/health");
    logSuccess('✅ Treino pronto!')
  }
}

async function awaitAdmMs() {
  const containerAdmMsName = await findAdmMsContainer();
  if (containerAdmMsName) {
    logInfo('⏳ Aguardando o ADM-MS iniciar...');
    const url = await getContainerEnvVariable(
      containerAdmMsName,
      "URL_ADM_MS",
    );
    await healthCheck(url + "/v1/health");
    logSuccess('✅ ADM-MS pronto!');
  }
}


// Função para aguardar API Gateway
async function awaitApiGateway() {
  const container = await findContainerByImage("registry.gitlab.com/plataformazw/api-gateway");
  if (container) {
    logInfo('⏳ Aguardando o API Gateway iniciar...');
    await healthCheck("http://host.docker.internal:8205/health");
    logSuccess('✅ API Gateway pronto!');
  }
}

// Função para aguardar Discovery MS
async function awaitDiscoveryMs() {
  const container = await findContainerByImage("registry.gitlab.com/plataformazw/discovery-ms");
  if (container) {
    logInfo('⏳ Aguardando o Discovery MS iniciar...');
    await healthCheck("http://host.docker.internal:8101/health");
    logSuccess('✅ Discovery MS pronto!');
  }
}

// Função para aguardar Autenticacao MS
async function awaitAutenticacaoMs() {
  const container = await findContainerByImage("registry.gitlab.com/plataformazw/autenticacao-ms");
  if (container) {
    logInfo('⏳ Aguardando o Autenticacao MS iniciar...');
    await healthCheck("http://host.docker.internal:8100/health");
    logSuccess('✅ Autenticacao MS pronto!');
  }
}

// Função para aguardar Login
async function awaitLogin() {
  const container = await findContainerByImage("registry.gitlab.com/plataformazw/login/tomcat");
  if (container) {
    logInfo('⏳ Aguardando o Login iniciar...');
    await healthCheck("http://host.docker.internal:8204/LoginApp/health");
    logSuccess('✅ Login pronto!');
  }
}

// Função para aguardar API
async function awaitApi() {
  const container = await findContainerByImage("registry.gitlab.com/plataformazw/api");
  if (container) {
    logInfo('⏳ Aguardando a API iniciar...');
    await healthCheck("http://host.docker.internal:8203/API-ZillyonWeb/prest/health");
    logSuccess('✅ API pronta!');
  }
}

// Função para aguardar Conversas AI API
async function awaitConversasAiApi() {
  const container = await findContainerByImage("registry.gitlab.com/plataformazw/ia/orion/api");
  if (container) {
    logInfo('⏳ Aguardando o Conversas AI API iniciar...');
    await healthCheck("http://host.docker.internal:8300/health/");
    logSuccess('✅ Conversas AI API pronto!');
  }
}

// Função para aguardar PostgreSQL
async function awaitPostgres() {
  const container = await findContainerByImage("registry.gitlab.com/plataformazw/docker-pacto/postgres");
  if (container) {
    logInfo('⏳ Aguardando o PostgreSQL iniciar...');
    await healthCheck("tcp:host.docker.internal:5432");
    logSuccess('✅ PostgreSQL pronto!');
  }
}

// Função para aguardar Redis
async function awaitRedis() {
  const container = await findContainerByImage("redis");
  if (container) {
    logInfo('⏳ Aguardando o Redis iniciar...');
    await healthCheck("tcp:host.docker.internal:6379");
    logSuccess('✅ Redis pronto!');
  }
}

async function waitStartContainers() {
  logInfo('🚀 Iniciando verificação de saúde dos serviços...');

  // Serviços de infraestrutura primeiro
  await awaitPostgres();
  await awaitRedis();

  // Serviços principais
  await awaitZw();
  await awaitTreino();
  await awaitApi();
  await awaitLogin();

  // Microserviços
  await awaitDiscoveryMs();
  await awaitAutenticacaoMs();
  await awaitAdmMs();
  await awaitApiGateway();

  // Serviços de IA
  await awaitConversasAiApi();

  logSuccess('🎉 Todos os serviços estão prontos e saudáveis!');
}

async function findAdmMsContainer() {
  return findContainerByImage("registry.gitlab.com/plataformazw/adm-ms:");
}

async function findTreinoContainer() {
  return findContainerByImage("registry.gitlab.com/plataformazw/treino/tomcat:");
}

// Função genérica para encontrar container por imagem
async function findContainerByImage(imagePattern) {
  return new Promise((resolve, reject) => {
    exec("docker ps", (err, stdout, stderr) => {
      if (err) {
        return reject(`Erro ao executar o comando Docker: ${stderr}`);
      }

      const lines = stdout.split("\n").slice(1); // Remove o cabeçalho
      const container = lines.find((line) => line.includes(imagePattern));

      if (container) {
        const containerName = container.split(" ")[0];
        resolve(containerName);
      } else {
        resolve(null);
      }
    });
  });
}

async function findZwContainer() {
  return findContainerByImage("registry.gitlab.com/plataformazw/zw/tomcat:");
}

async function getContainerEnvVariable(containerName, variableName) {
  return new Promise((resolve, reject) => {
    exec(
      `docker exec ${containerName} printenv ${variableName}`,
      (err, stdout, stderr) => {
        if (err) {
          logError(
            `💥 Erro ao obter a variável de ambiente ${variableName}: ${err.message}`,
          );
          reject(err);
          return;
        }
        resolve(stdout.trim());
      },
    );
  });
}

async function healthCheck(url) {
  let resource = url
    .replace("http://", "http-get://")
    .replace("https://", "https-get://");

  const opts = {
    resources: [resource],
    interval: 5000,
    timeout: 360000,
    verbose: false,
  };

  await waitOn(opts);
}



module.exports = {
  getRunningContainers,
  healthCheck,
  getContainerEnvVariable,
  waitStartContainers,
  findZwContainer,
  findTreinoContainer,
  findAdmMsContainer,
  findContainerByImage
};
