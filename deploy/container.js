const { exec } = require('child_process');
const waitOn = require('wait-on');
const chalk = require('chalk');

async function getRunningContainers() {
  return new Promise((resolve, reject) => {
    exec('docker ps --filter "status=running" --filter "name=dev-tools-" --format "{{.ID}}|{{.Names}}|{{.Image}}|{{.Status}}"', (err, stdout, stderr) => {
      if (err) {
        reject(`Erro ao listar os containers em execução: ${err}`);
        return;
      }
      const containers = stdout.split('\n').filter(line => line).map(line => {
        const [id, name, image, status] = line.split('|');
        return { id, name, image, status };
      });
      resolve(containers);
    });
  });
}

async function awaitZw() {
  const zwContainer = await findZwContainer();
  if (zwContainer) {
    console.info('Aguardando o ZW iniciar...');
    const urlZw = await getContainerEnvVariable(zwContainer, "URL_ZW");
    await healthCheck(urlZw + "/prest/health");
    console.info('ZW pronto')
  }
}

async function awaitTreino() {
  const containerTreinoName = await findTreinoContainer();
  if (containerTreinoName) {
    console.info('Aguardando o Treino iniciar...');
    const urlTreino = await getContainerEnvVariable(
      containerTreinoName,
      "URL_TREINO",
    );
    await healthCheck(urlTreino + "/prest/health");
    console.log('Treino pronto')
  }
}

async function awaitAdmMs() {
  const containerAdmMsName = await findAdmMsContainer();
  if (containerAdmMsName) {
    console.info('Aguardando o ADM-MS iniciar...');
    const url = await getContainerEnvVariable(
      containerAdmMsName,
      "URL_ADM_MS",
    );
    await healthCheck(url + "/v1/health");
    console.log('ADM-MS pronto');
  }
}


async function waitStartContainers() {
  await awaitZw();
  await awaitTreino();
  await awaitAdmMs();
}

async function findAdmMsContainer() {
  return new Promise((resolve, reject) => {
    exec("docker ps", (err, stdout, stderr) => {
      if (err) {
        return reject(`Erro ao executar o comando Docker: ${stderr}`);
      }

      const lines = stdout.split("\n").slice(1); // Remove o cabeçalho

      for (const line of lines) {
        const columns = line.split(/\s+/);
        const imageName = columns[1];

        if (imageName && imageName.startsWith("registry.gitlab.com/plataformazw/treino/tomcat:")) {
          resolve(columns[0]);
          return;
        }
      }

      resolve(null);
    });
  });
}

async function findTreinoContainer() {
  return new Promise((resolve, reject) => {
    exec("docker ps", (err, stdout, stderr) => {
      if (err) {
        return reject(`Erro ao executar o comando Docker: ${stderr}`);
      }

      const lines = stdout.split("\n").slice(1); // Remove o cabeçalho

      for (const line of lines) {
        const columns = line.split(/\s+/);
        const imageName = columns[1];

        if (imageName && imageName.startsWith("registry.gitlab.com/plataformazw/treino/tomcat:")) {
          resolve(columns[0]);
          return;
        }
      }

      resolve(null);
    });
  });
}

async function findZwContainer() {
  return new Promise((resolve, reject) => {
    // Executa o comando 'docker ps' para listar os containers sem o 'format'
    exec("docker ps", (err, stdout, stderr) => {
      if (err) {
        return reject(`Erro ao executar o comando Docker: ${stderr}`);
      }

      // Split the output into lines (skipping the first header line)
      const lines = stdout.split("\n").slice(1); // Skips the first header line

      // Loop through the lines to find the container with the desired image
      const zwContainer = lines.find((line) =>
        line.includes("registry.gitlab.com/plataformazw/zw/tomcat:")
      );

      if (zwContainer) {
        // Extract the container name (the first word of the line)
        const containerName = zwContainer.split(" ")[0];
        resolve(containerName);
      } else {
        resolve(null); // Nenhum container encontrado
      }
    });
  });
}

async function getContainerEnvVariable(containerName, variableName) {
  return new Promise((resolve, reject) => {
    exec(
      `docker exec ${containerName} printenv ${variableName}`,
      (err, stdout, stderr) => {
        if (err) {
          console.error(
            `Erro ao obter a variável de ambiente ${variableName}: ${err.message}`,
          );
          reject(err);
          return;
        }
        resolve(stdout.trim());
      },
    );
  });
}

async function healthCheck(url) {
  let resource = url
    .replace("http://", "http-get://")
    .replace("https://", "https-get://");

  const opts = {
    resources: [resource],
    interval: 5000,
    timeout: 360000,
    verbose: false,
  };

  await waitOn(opts);
}



module.exports = { getRunningContainers, healthCheck, getContainerEnvVariable, waitStartContainers, findZwContainer, findTreinoContainer };
