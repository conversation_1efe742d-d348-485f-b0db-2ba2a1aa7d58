const { exec } = require('child_process');
const waitOn = require('wait-on');
const chalk = require('chalk');
const { logSuccess, logError, logInfo, logWarning } = require('../util/log');

async function getRunningContainers() {
  return new Promise((resolve, reject) => {
    exec('docker ps --filter "status=running" --filter "name=dev-tools-" --format "{{.ID}}|{{.Names}}|{{.Image}}|{{.Status}}"', (err, stdout, stderr) => {
      if (err) {
        reject(`Erro ao listar os containers em execução: ${err}`);
        return;
      }
      const containers = stdout.split('\n').filter(line => line).map(line => {
        const [id, name, image, status] = line.split('|');
        return { id, name, image, status };
      });
      resolve(containers);
    });
  });
}

async function awaitZw() {
  const zwContainer = await findZwContainer();
  if (zwContainer) {
    logInfo('⏳ Aguardando o ZW iniciar...');
    const urlZw = await getContainerEnvVariable(zwContainer, "URL_ZW");
    await healthCheckWithLogs(urlZw + "/prest/health", zwContainer, "ZW");
    logSuccess('✅ ZW pronto!')
  }
}

async function awaitTreino() {
  const containerTreinoName = await findTreinoContainer();
  if (containerTreinoName) {
    logInfo('⏳ Aguardando o Treino iniciar...');
    const urlTreino = await getContainerEnvVariable(
      containerTreinoName,
      "URL_TREINO",
    );
    await healthCheckWithLogs(urlTreino + "/prest/health", containerTreinoName, "Treino");
    logSuccess('✅ Treino pronto!')
  }
}

async function awaitAdmMs() {
  const containerAdmMsName = await findAdmMsContainer();
  if (containerAdmMsName) {
    logInfo('⏳ Aguardando o ADM-MS iniciar...');
    const url = await getContainerEnvVariable(
      containerAdmMsName,
      "URL_ADM_MS",
    );
    await healthCheckWithLogs(url + "/v1/health", containerAdmMsName, "ADM-MS");
    logSuccess('✅ ADM-MS pronto!');
  }
}


// Função para aguardar API Gateway
async function awaitApiGateway() {
  const container = await findContainerByImage("registry.gitlab.com/plataformazw/api-gateway");
  if (container) {
    logInfo('⏳ Aguardando o API Gateway iniciar...');
    await healthCheckWithLogs("http://host.docker.internal:8205/health", container, "API Gateway");
    logSuccess('✅ API Gateway pronto!');
  }
}

// Função para aguardar Discovery MS
async function awaitDiscoveryMs() {
  const container = await findContainerByImage("registry.gitlab.com/plataformazw/discovery-ms");
  if (container) {
    logInfo('⏳ Aguardando o Discovery MS iniciar...');
    await healthCheckWithLogs("http://host.docker.internal:8101/health", container, "Discovery MS");
    logSuccess('✅ Discovery MS pronto!');
  }
}

// Função para aguardar Autenticacao MS
async function awaitAutenticacaoMs() {
  const container = await findContainerByImage("registry.gitlab.com/plataformazw/autenticacao-ms");
  if (container) {
    logInfo('⏳ Aguardando o Autenticacao MS iniciar...');
    await healthCheckWithLogs("http://host.docker.internal:8100/health", container, "Autenticacao MS");
    logSuccess('✅ Autenticacao MS pronto!');
  }
}

// Função para aguardar Login
async function awaitLogin() {
  const container = await findContainerByImage("registry.gitlab.com/plataformazw/login/tomcat");
  if (container) {
    logInfo('⏳ Aguardando o Login iniciar...');
    await healthCheckWithLogs("http://host.docker.internal:8204/LoginApp/health", container, "Login");
    logSuccess('✅ Login pronto!');
  }
}

// Função para aguardar API
async function awaitApi() {
  const container = await findContainerByImage("registry.gitlab.com/plataformazw/api");
  if (container) {
    logInfo('⏳ Aguardando a API iniciar...');
    await healthCheckWithLogs("http://host.docker.internal:8203/API-ZillyonWeb/prest/v2/vendas/health", container, "API");
    logSuccess('✅ API pronta!');
  }
}

// Função para aguardar Conversas AI API
async function awaitConversasAiApi() {
  const container = await findContainerByImage("registry.gitlab.com/plataformazw/ia/orion/api");
  if (container) {
    logInfo('⏳ Aguardando o Conversas AI API iniciar...');
    await healthCheckWithLogs("http://host.docker.internal:8300/health/", container, "Conversas AI API");
    logSuccess('✅ Conversas AI API pronto!');
  }
}

// Função para aguardar PostgreSQL
async function awaitPostgres() {
  const container = await findContainerByImage("registry.gitlab.com/plataformazw/docker-pacto/postgres");
  if (container) {
    logInfo('⏳ Aguardando o PostgreSQL iniciar...');
    try {
      await healthCheck("tcp:host.docker.internal:5432");
      logSuccess('✅ PostgreSQL pronto!');
    } catch (error) {
      logError(`💥 PostgreSQL health check falhou: ${error.message}`);
      await showContainerLogsOnFailure(container, "PostgreSQL");
      throw error;
    }
  }
}

// Função para aguardar Redis
async function awaitRedis() {
  const container = await findContainerByImage("redis");
  if (container) {
    logInfo('⏳ Aguardando o Redis iniciar...');
    try {
      await healthCheck("tcp:host.docker.internal:6379");
      logSuccess('✅ Redis pronto!');
    } catch (error) {
      logError(`💥 Redis health check falhou: ${error.message}`);
      await showContainerLogsOnFailure(container, "Redis");
      throw error;
    }
  }
}

// Função para aguardar CRM MS
async function awaitCrmMs() {
  const container = await findContainerByImage("registry.gitlab.com/plataformazw/flash/crm-ms");
  if (container) {
    logInfo('⏳ Aguardando o CRM MS iniciar...');
    await healthCheckWithLogs("http://host.docker.internal:8110/crm-ms/v1/health", container, "CRM MS");
    logSuccess('✅ CRM MS pronto!');
  }
}

// Função para aguardar ADM Core MS
async function awaitAdmCoreMs() {
  const container = await findContainerByImage("registry.gitlab.com/plataformazw/adm-core-ms");
  if (container) {
    logInfo('⏳ Aguardando o ADM Core MS iniciar...');
    await healthCheckWithLogs("http://host.docker.internal:8111/health", container, "ADM Core MS");
    logSuccess('✅ ADM Core MS pronto!');
  }
}

// Função para aguardar Financeiro MS
async function awaitFinanceiroMs() {
  const container = await findContainerByImage("registry.gitlab.com/plataformazw/financeiro-ms");
  if (container) {
    logInfo('⏳ Aguardando o Financeiro MS iniciar...');
    await healthCheckWithLogs("http://host.docker.internal:8124/financeiro-ms/v1/health", container, "Financeiro MS");
    logSuccess('✅ Financeiro MS pronto!');
  }
}

// Função para aguardar Plano MS
async function awaitPlanoMs() {
  const container = await findContainerByImage("registry.gitlab.com/plataformazw/plano-java-ms");
  if (container) {
    logInfo('⏳ Aguardando o Plano MS iniciar...');
    await healthCheckWithLogs("http://host.docker.internal:8115/health", container, "Plano MS");
    logSuccess('✅ Plano MS pronto!');
  }
}

async function waitStartContainers() {
  logInfo('🚀 Iniciando verificação de saúde dos serviços...');

  // Serviços de infraestrutura primeiro
  await awaitPostgres();
  await awaitRedis();

  // Serviços principais
  await awaitZw();
  await awaitTreino();
  await awaitApi();
  await awaitLogin();

  // Microserviços
  await awaitDiscoveryMs();
  await awaitAutenticacaoMs();
  await awaitAdmMs();
  await awaitAdmCoreMs();
  await awaitApiGateway();

  // Microserviços de negócio
  await awaitCrmMs();
  await awaitFinanceiroMs();
  await awaitPlanoMs();

  // Serviços de IA
  await awaitConversasAiApi();

  logSuccess('🎉 Todos os serviços estão prontos e saudáveis!');
}

async function findAdmMsContainer() {
  return findContainerByImage("registry.gitlab.com/plataformazw/adm-ms:");
}

async function findTreinoContainer() {
  return findContainerByImage("registry.gitlab.com/plataformazw/treino/tomcat:");
}

// Função genérica para encontrar container por imagem
async function findContainerByImage(imagePattern) {
  return new Promise((resolve, reject) => {
    exec("docker ps", (err, stdout, stderr) => {
      if (err) {
        return reject(`Erro ao executar o comando Docker: ${stderr}`);
      }

      const lines = stdout.split("\n").slice(1); // Remove o cabeçalho
      const container = lines.find((line) => line.includes(imagePattern));

      if (container) {
        const containerName = container.split(" ")[0];
        resolve(containerName);
      } else {
        resolve(null);
      }
    });
  });
}

async function findZwContainer() {
  return findContainerByImage("registry.gitlab.com/plataformazw/zw/tomcat:");
}

async function getContainerEnvVariable(containerName, variableName) {
  return new Promise((resolve, reject) => {
    exec(
      `docker exec ${containerName} printenv ${variableName}`,
      (err, stdout, stderr) => {
        if (err) {
          logError(
            `💥 Erro ao obter a variável de ambiente ${variableName}: ${err.message}`,
          );
          reject(err);
          return;
        }
        resolve(stdout.trim());
      },
    );
  });
}

// Função para obter logs de um container
async function getContainerLogs(containerName, lines = 50) {
  return new Promise((resolve, reject) => {
    exec(`docker logs --tail ${lines} ${containerName}`, (err, stdout, stderr) => {
      if (err) {
        logError(`💥 Erro ao obter logs do container ${containerName}: ${err.message}`);
        reject(err);
        return;
      }
      resolve({ stdout, stderr });
    });
  });
}

// Função para mostrar logs do container quando health check falhar
async function showContainerLogsOnFailure(containerName, serviceName) {
  try {
    logWarning(`📜 Obtendo logs do container ${serviceName} para diagnóstico...`);
    const { stdout, stderr } = await getContainerLogs(containerName, 100);

    if (stdout) {
      logInfo(`📋 Logs do ${serviceName} (stdout):`);
      console.log(chalk.gray(stdout));
    }

    if (stderr) {
      logWarning(`⚠️ Logs de erro do ${serviceName} (stderr):`);
      console.log(chalk.red(stderr));
    }

    if (!stdout && !stderr) {
      logInfo(`ℹ️ Nenhum log encontrado para o container ${serviceName}`);
    }
  } catch (error) {
    logError(`💥 Erro ao obter logs do container ${serviceName}: ${error.message}`);
  }
}

// Função de health check com tratamento de erro e exibição de logs
async function healthCheckWithLogs(url, containerName, serviceName, timeout = 360000) {
  let resource = url
    .replace("http://", "http-get://")
    .replace("https://", "https-get://");

  const opts = {
    resources: [resource],
    interval: 5000,
    timeout: timeout,
    verbose: false,
  };

  try {
    await waitOn(opts);
  } catch (error) {
    logError(`💥 Health check falhou para ${serviceName}: ${error.message}`);

    if (containerName) {
      await showContainerLogsOnFailure(containerName, serviceName);
    }

    // Re-throw o erro para que o processo pai possa decidir o que fazer
    throw new Error(`Health check falhou para ${serviceName}: ${error.message}`);
  }
}

// Função original mantida para compatibilidade
async function healthCheck(url) {
  let resource = url
    .replace("http://", "http-get://")
    .replace("https://", "https-get://");

  const opts = {
    resources: [resource],
    interval: 5000,
    timeout: 360000,
    verbose: false,
  };

  await waitOn(opts);
}



module.exports = {
  getRunningContainers,
  healthCheck,
  healthCheckWithLogs,
  getContainerLogs,
  showContainerLogsOnFailure,
  getContainerEnvVariable,
  waitStartContainers,
  findZwContainer,
  findTreinoContainer,
  findAdmMsContainer,
  findContainerByImage,
  awaitCrmMs,
  awaitAdmCoreMs,
  awaitFinanceiroMs,
  awaitPlanoMs
};
