const os = require('os');

function getHostIp(){
    let ip;
    const networkInterfaces = os.networkInterfaces();
    for (let name of Object.keys(networkInterfaces)) {
        for (let net of networkInterfaces[name]) {
            if (!net.internal && net.family === 'IPv4') {
                ip = net.address;
                break;
            }
        }
        if (ip) {
            break;
        }
    }

    return ip;
}

function getPipelineServerIp(testEnvironment = 'prod'){
    if(testEnvironment === 'prod')
        return '**********';

    if(testEnvironment === 'staging')
        return '************'

    throw new Error(`Ambiente de teste ${testEnvironment} não encontrado`);
}

function getPipelineServerDomains(testEnvironment = 'prod'){
    if(testEnvironment === 'prod')
        return getPipelineServerProdDomains();

    if(testEnvironment === 'staging')
        return getPipelineServerStagingDomains();

    throw new Error(`Ambiente de teste ${testEnvironment} não encontrado`);
}

function getPipelineServerStagingDomains(){
    return [
        'traefik.test.pactosolucoes.com.br',
        'portainer.test.pactosolucoes.com.br',
        'storage.test.pactosolucoes.com.br',
        'storage-api.test.pactosolucoes.com.br',
        'cypress.test.pactosolucoes.com.br',
        'director-cypress.test.pactosolucoes.com.br',
        'api-cypress.test.pactosolucoes.com.br',
        'test.pactosolucoes.com.br',
        'grafana.test.pactosolucoes.com.br'
    ]
}

function getPipelineServerProdDomains(){
    return [
        'traefik.pactoteste.com',
        'portainer.pactoteste.com',
        'storage.pactoteste.com',
        'storage-api.pactoteste.com',
        'cypress.pactoteste.com',
        'director-cypress.pactoteste.com',
        'api-cypress.pactoteste.com',
        'pactoteste.com',
        'grafana.pactoteste.com'
    ]
}

module.exports = { getHostIp, getPipelineServerIp, getPipelineServerDomains };