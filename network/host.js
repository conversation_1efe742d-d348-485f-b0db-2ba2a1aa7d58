const os = require('os');
const fs = require('fs');
const ping = require('ping');
const sudo = require('@vscode/sudo-prompt');
const { getHostIp } = require('./ip.js');
const { log } = require('../util/log.js');

async function addDomainsToHost(domains, ip) {
    try {
        let hostsPath;
        domains = Array.isArray(domains) ? domains : [domains];
        if (os.platform() === 'win32') {
            hostsPath = 'C:\\Windows\\System32\\drivers\\etc\\hosts';
        } else if (os.platform() === 'linux' || os.platform() === 'darwin'){
            hostsPath = '/etc/hosts';
        } else {
            throw new Error('Unsupported platform', os.platform());
        }
    
        let fileContent = fs.readFileSync(hostsPath, 'utf-8');
        const headerComment =  '# Added by Pacto Host Manager';
        const footerComment = '# Pacto Host Manager End';

        log('pacto:network', `Conteúdo do arquivo de hosts antes da alteração:`, fileContent);
    
        let startIndex = fileContent.indexOf(headerComment);
        let endIndex = fileContent.indexOf(footerComment);

        let domainsNotExists = [];
        domains.forEach(domain => {
            const domainRegex = new RegExp(`\\s*${ip}\\s+${domain}\\s*`);
            if (!fileContent.match(domainRegex)) {
             domainsNotExists.push(domain);
            }
        });

        log('pacto:network', `Dominios que serão configurados:`, domainsNotExists);
        
        let existingDomains = [];
        if(startIndex !== -1 && endIndex !== -1){
            existingDomains = fileContent.slice(startIndex + headerComment.length, endIndex).split(os.EOL).filter(line => line.trim() !== '').map(line => {
                const [ip, domain] = line.trim().split(' ');
                return { ip, domain };
            });
            fileContent = fileContent.slice(0, startIndex) + fileContent.slice(endIndex + footerComment.length);
        }
        
        log('pacto:network', `Dominios que já estão configurados:`, existingDomains);

        if(domainsNotExists.length > 0 || existingDomains.length > 0){
            fs.writeFileSync(hostsPath, fileContent + os.EOL + headerComment + os.EOL + [...new Set([...existingDomains.map(({ domain }) => domain), ...domainsNotExists])].map(domain => {
                const existingDomain = existingDomains.find(({ domain: d }) => d === domain);
                return `${existingDomain ? existingDomain.ip : ip} ${domain}`;
            }).join(os.EOL) + os.EOL + footerComment + os.EOL);
        }

        log('pacto:network', `Conteúdo do arquivo de hosts após a alteração:`, fileContent);
        
    } catch (error) {
        if(error.code === 'EACCES' || error.code === 'EPERM'){
            console.error('Acesso negado ao editar arquivo de hosts. Você precisa executar este comando administrador ou');
            process.exit(1);
        }else{
            throw error;
        }
    }
}; 

function getHostsPath(){
    if (os.platform() === 'win32') {
        return 'C:\\Windows\\System32\\drivers\\etc\\hosts';
    } else if (os.platform() === 'linux' || os.platform() === 'darwin'){
        return '/etc/hosts';
    } else {
        throw new Error('Unsupported platform', os.platform());
    }

}

function checkDomainsDoNotExistInHost(domains, ip){
    let domainsNotExists = [];
    const fileContent = fs.readFileSync(getHostsPath(), 'utf-8');
    
    domains.forEach(domain => {
        const domainRegex = new RegExp(`\\s*${ip}\\s+${domain}\\s*`);
        if (!fileContent.match(domainRegex)) {
         domainsNotExists.push(domain);
        }
    });
    return domainsNotExists;
}

async function addHostDockerInternal() {
    try{
        const res = await ping.promise.probe('host.docker.internal');
        if (!res.alive) {
            const platform = os.platform();
            const ip = getHostIp();
            const hostsPath = platform === 'win32' ? '%SystemRoot%\\System32\\drivers\\etc\\hosts' : '/etc/hosts';
            const hostEntry = `${ip} host.docker.internal`;

            const script = platform === 'win32'
                ? `
                    if findstr /C:"host.docker.internal" ${hostsPath} >nul; then (
                        powershell -Command "(Get-Content ${hostsPath}) -replace '.*host.docker.internal.*', '${hostEntry}' | Set-Content ${hostsPath}"
                    ) else (
                        echo ${hostEntry} >> ${hostsPath}
                    )
                `
                : `
                    if grep -q "host.docker.internal" ${hostsPath}; then
                        sudo sed -i'' -e "s/.*host.docker.internal.*/${hostEntry}/" ${hostsPath}
                    else
                        echo "${hostEntry}" | sudo tee -a ${hostsPath}
                    fi
                `;

            sudo.exec(script, { name: 'Pacto' }, (error, stdout, stderr) => {
                if (error) {
                    console.error(`Error: ${error.message}`);
                    process.exit(1);
                }
                if (stderr) {
                    console.error(`stderr: ${stderr}`);
                    process.exit(1);
                }
                log('pacto:network', `Host 'host.docker.internal' configured successfully!`, stdout);
                console.log(`Host 'host.docker.internal' configured successfully!`);
            });
        }else{
            console.log('Host docker.internal já configurado');
        }
    }catch(error){
        console.error('Erro ao configurar host docker.internal:', error.message);
        process.exit(1);
    }
}

module.exports = { addDomainsToHost, getHostsPath, checkDomainsDoNotExistInHost, addHostDockerInternal };
