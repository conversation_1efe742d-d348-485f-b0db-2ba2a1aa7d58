const { addDomainsToHost, checkDomainsDoNotExistInHost, addHostDockerInternal } = require('./host.js');
const { getPipelineServerIp } = require('./ip.js');
const { getPipelineServerDomains } = require('./domain.js');
const { log } = require('../util/log.js');
const path = require('path');
const { root } = require('../util/path.js');
const sudo = require('@vscode/sudo-prompt');

function addNetworkCommands(program) {
    const network = program
        .command('network')
        .alias('n')
        .description('Configura a rede do seu computador para conectar em servidores da Pacto');

    network.command('host-docker-internal')
        .alias('hd')
        .description('Configura o host host.docker.internal na sua maquina')
        .action((cmd) => {
            addHostDockerInternal();
        });

    network.command('hosts-pipe')
        .alias('hp')
        .description('Configura a conexão com servidores de teste e pipeline da Pacto')
        .option('-d, --domains <domains>', 'Domínios para configurar')
        .option('-i, --ip <ip>', 'IP para configurar')
        .action((cmd) => {
            const domains = cmd.domains ? cmd.domains.split(',') : getPipelineServerDomains();
            const ip = cmd.ip || getPipelineServerIp();
            const nonExistentDomains = checkDomainsDoNotExistInHost(domains, ip);
            if (nonExistentDomains.length > 0) {
                log('pacto:network', 'Os seguintes domínios não existem no arquivo de hosts:', nonExistentDomains);
                 
                    const cmdConfigHost = getCommandConfigHost(nonExistentDomains, ip);
                    log('pacto:network', `Comando para configurar hosts:`, cmdConfigHost);
                    sudo.exec(cmdConfigHost, {name: 'Pacto'}, (error, stdout, stderr) => {
                        if (error) {
                        console.log(`error: ${error.message}`);
                        process.exit(1);
                        }
                        if (stderr) {
                        console.error(`stderr: ${stderr}`);
                        process.exit(1);
                        }
                        log('pacto:network', `Arquivo de hosts configurado com sucesso!`, stdout);
                        console.log(`Arquivo de hosts configurado com sucesso!`);
                    });

            } else {
                console.log('Todos os domínios já existem no arquivo de hosts');
            }
        });

    network.command('hosts-pipe-add')
        .alias('hpa')
        .description('Adiciona os hosts de teste e pipeline da Pacto ao arquivo de hosts. É necessário permissão de administrador.')
        .option('-d, --domains <domains>', 'Domínios para configurar')
        .option('-i, --ip <ip>', 'IP para configurar')
        .action((cmd) => {
            const domains = cmd.domains ? cmd.domains.split(',') : getPipelineServerDomains();
            const ip = cmd.ip || getPipelineServerIp();
            log('pacto:network', `Command hosts-pipe-add: Adicionando domínios com IP ${ip} ao arquivo de hosts e comentário`, domains);
            addDomainsToHost(domains, ip);
        });
}

function getCommandConfigHost(domains, ip) {
    const domainsParam = domains ? `--domains ${domains.join(',')}` : '';
    const ipParam = ip ? `--ip ${ip}` : '';
    const indexFilePath = path.resolve(root(), `index.js`);
    return `node ${indexFilePath} network hosts-pipe-add ${domainsParam} ${ipParam}`;
}

module.exports = { addNetworkCommands };
