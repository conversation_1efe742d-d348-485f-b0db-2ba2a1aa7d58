const net = require('net');

function getDebugPort() {
    return getAvailablePort(9000, 9100);
}

function getAvailablePort(startPort, endPort) {
    return new Promise((resolve, reject) => {
        const portRange = Array(endPort - startPort + 1).fill().map((_, i) => startPort + i);

        const checkNextPort = (index) => {
            if (index >= portRange.length) {
                reject('No available ports in the given range');
                return;
            }

            const port = portRange[index];
            const server = net.createServer();

            server.once('error', (err) => {
                if (err.code === 'EADDRINUSE') {
                    // Port is currently in use, check the next one
                    checkNextPort(index + 1);
                } else {
                    // Some other error occurred, reject the promise
                    reject(err);
                }
            });

            server.once('listening', () => {
                // Port is available, resolve the promise
                server.close();
                resolve(port);
            });

            server.listen(port);
        };

        checkNextPort(0);
    });
}

module.exports = { getDebugPort, getAvailablePort };