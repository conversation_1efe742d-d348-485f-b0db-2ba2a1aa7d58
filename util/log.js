function log(moduleTag, message, object){
    let debug = false;
    if (process.env.DEBUG && process.env.DEBUG.includes(moduleTag) || process.env.DEBUG === 'pacto:' || process.env.DEBUG === '*'){
        debug = true;
    }

    if(debug){
        let stack = new Error().stack;
        let caller = stack.split('\n')[2].trim();
    
        if(!object){
            console.log(`[${moduleTag}] ${caller} ${message}`);
            return;
        }
        
        console.log(`[${moduleTag}] ${caller} ${message}`, object);
    }
}

module.exports = { log };