const chalk = require('chalk');

function log(moduleTag, message, object){
    let debug = false;
    if (process.env.DEBUG && process.env.DEBUG.includes(moduleTag) || process.env.DEBUG === 'pacto:' || process.env.DEBUG === '*'){
        debug = true;
    }

    if(debug){
        let stack = new Error().stack;
        let caller = stack.split('\n')[2].trim();

        if(!object){
            console.log(`[${moduleTag}] ${caller} ${message}`);
            return;
        }

        console.log(`[${moduleTag}] ${caller} ${message}`, object);
    }
}

// Funções de log coloridas e divertidas
function logSuccess(message, details) {
    console.log(chalk.green(`🎉 ${message}`));
    if (details) {
        console.log(chalk.gray(details));
    }
}

function logError(message, details) {
    console.log(chalk.red(`💥 ${message}`));
    if (details) {
        console.log(chalk.gray(details));
    }
}

function logWarning(message, details) {
    console.log(chalk.yellow(`⚠️  ${message}`));
    if (details) {
        console.log(chalk.gray(details));
    }
}

function logInfo(message, details) {
    console.log(chalk.blue(`ℹ️  ${message}`));
    if (details) {
        console.log(chalk.gray(details));
    }
}

function logProcess(message, details) {
    console.log(chalk.cyan(`🗑️  ${message}`));
    if (details) {
        console.log(chalk.gray(details));
    }
}

function logContainer(message, details) {
    console.log(chalk.magenta(`🐳 ${message}`));
    if (details) {
        console.log(chalk.gray(details));
    }
}

module.exports = {
    log,
    logSuccess,
    logError,
    logWarning,
    logInfo,
    logProcess,
    logContainer
};