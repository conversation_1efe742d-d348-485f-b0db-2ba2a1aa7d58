const fs = require('fs');

/**
 * Verifica se o código está sendo executado dentro de um container Docker
 * @returns {boolean} true se estiver em um container, false caso contrário
 */
function isRunningInContainer() {
  try {
    // Verifica a existência do arquivo /.dockerenv, que é criado pelo Docker
    return fs.existsSync('/.dockerenv');
  } catch (error) {
    // Em caso de erro, assume que não está em um container
    return false;
  }
}

module.exports = {
  isRunningInContainer
};
