const { setValue, getValue } = require('./storage');
const { execSync } = require('child_process');
const { getGitlabToken } = require('../git/gitlab');
const { logSuccess, logError, logInfo, logWarning } = require('./log');
const inquirer = require('inquirer');
const chalk = require('chalk');

function dockerLoginEnv(){

    const { env } = process;

    const username = env.CI_USER;
    const password = env.CI_USER_PASS;
    const registry = env.CI_REGISTRY;

    if (username && password && registry) {
        logInfo('🐳 Usando variáveis de ambiente para login no Docker');

        getValue('dockerLogin', false);
        if (getValue('dockerLogin', false)) {
            logSuccess('✅ Docker login já realizado');
            return;
        }

        try {
            execSync(`echo ${password} | docker login ${registry} -u "${username}" --password-stdin`, { stdio: 'inherit' });
            logSuccess('🎉 Docker login successful');
            setValue('dockerLogin', true);
            setValue('dockerLoginGitlab', true);
        } catch (error) {
            logError('💥 Docker login failed:', error.message);
            process.exit(1);
        }
    }
}

async function dockerLoginGitlab() {
    const gitlabRegistry = 'registry.gitlab.com';
    logInfo('🔍 Verificando login no gitlab docker registry...');
    dockerLoginEnv();

    // Verificar se já está logado no registry do GitLab
    if (getValue('dockerLoginGitlab', false)) {
        logSuccess('✅ Docker login no GitLab registry já realizado');
        return;
    }

    logInfo('🔍 Verificando autenticação no Docker registry do GitLab...');

    try {
        try {
            // Verificar autenticação usando docker system info
            const dockerInfo = execSync('docker system info', { stdio: 'pipe' }).toString();
            
            // Verificar se o registry do GitLab está nas informações (funciona em todas as plataformas)
            if (dockerInfo.includes(gitlabRegistry)) {
                logSuccess('✅ Usuário já autenticado no Docker registry do GitLab');
                setValue('dockerLoginGitlab', true);
                return;
            }

            logWarning('⚠️ Usuário não autenticado no Docker registry do GitLab');
            // Continuar para fazer login
        } catch (error) {
            // Se falhar, precisamos fazer login
        }

        // Obter token do GitLab
        const gitlabToken = await getGitlabToken();

        if (!gitlabToken) {
            throw new Error('Token do GitLab não encontrado');
        }

        // Obter username do GitLab
        let gitlabUsername = getValue('gitlab_username', process.env.GITLAB_USERNAME);

        if (!gitlabUsername) {
            const answers = await inquirer.prompt([
                {
                    type: 'input',
                    name: 'username',
                    message: 'Por favor, insira seu username do GitLab:',
                    validate: (input) => {
                        if (!input.trim()) {
                            return 'Username é obrigatório';
                        }
                        return true;
                    }
                }
            ]);
            gitlabUsername = answers.username;
            setValue('gitlab_username', gitlabUsername);
        }else{
            setValue('gitlab_username', gitlabUsername);
        }

        logInfo('🔐 Realizando login no Docker registry do GitLab...');

        // Fazer login no registry do GitLab
        execSync(`echo ${gitlabToken} | docker login ${gitlabRegistry} -u "${gitlabUsername}" --password-stdin`, { stdio: 'inherit' });

        logSuccess('🎉 Docker login no GitLab registry realizado com sucesso!');
        setValue('dockerLoginGitlab', true);

    } catch (error) {
        logError('💥 Erro ao fazer login no Docker registry do GitLab:', error.message);
        logError('📋 Certifique-se de que:');
        logError('1️⃣ Você tem um token válido do GitLab');
        logError('2️⃣ O token tem permissões para acessar o registry');
        logError('3️⃣ O Docker está rodando');
        process.exit(1);
    }
}

module.exports = {dockerLoginEnv, dockerLoginGitlab};
