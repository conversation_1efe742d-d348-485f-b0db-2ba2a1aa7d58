const fs = require('fs');
const path = require('path');
const root = require('./../util/path.js').root;

const defaultJsonPath = path.join(root(), 'storage.json');

function setValue(attr, value){
    if (!fs.existsSync(defaultJsonPath)) {
        fs.writeFileSync(defaultJsonPath, JSON.stringify({}));
    }

    let data = {};
    try {
        data = JSON.parse(fs.readFileSync(defaultJsonPath));
    } catch (error) {
        fs.writeFileSync(defaultJsonPath, JSON.stringify(data));
    }
    
    data[attr] = value;
    fs.writeFileSync(defaultJsonPath, JSON.stringify(data));
}

function getValue(attr, defaultValue = undefined){
    if (!fs.existsSync(defaultJsonPath)) {
        return null;
    }

    let data = JSON.parse(fs.readFileSync(defaultJsonPath));
    if(data[attr] === undefined || data[attr] === null || data[attr] === ''){
        return defaultValue;
    }
    return data[attr];
}

module.exports = { setValue, getValue };