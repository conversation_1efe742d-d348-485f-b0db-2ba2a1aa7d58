const { execSync } = require('child_process');
const path = require('path');

function commitMessage(){
    try {
        process.chdir(path.resolve(__dirname, '..','.git'));
        const commitMessage = execSync('git log -1 --pretty=%B').toString().trim();
        return commitMessage;
    } catch (error) {
        console.error('Erro ao obter a mensagem do commit:', error);
        return null;
    }
}

module.exports = { commitMessage };