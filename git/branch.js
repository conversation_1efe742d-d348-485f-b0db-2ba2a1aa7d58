const { execSync } = require('child_process');
const path = require('path');
const { root } = require('../util/path.js');

function getNormalizedBranchName(gitDirPath) {
    try {
        process.chdir(gitDirPath || getGitPath());
        return normalizeBranchName(getBranchName());
    } catch (error) {
        console.error('Erro ao obter o nome da branch git:', error);
        return null;
    }
}

function getBranchName(gitDirPath){
    process.chdir(gitDirPath || getGitPath());
    const branchName = execSync('git rev-parse --abbrev-ref HEAD').toString().trim();
    return branchName;
}

function normalizeBranchName(branchName){
    return branchName.replace(/[^\w\s]/gi, '-').trim().toLocaleLowerCase().replace(/\_/g, '-');
}

function getGitPath(){
    return path.resolve(root(), '.git');
}

function getBranchs(){
    try {
        process.chdir(getGitPath());
        const branchNames = execSync('git branch', { encoding: 'utf8' }).trim();
        return branchNames.split('\n').map(branch => branch.replace('*', '').trim()).filter(Boolean);
    } catch (error) {
        console.error('Erro ao obter o nome da branch git:', error);
        return null;
    }
}

module.exports = { getNormalizedBranchName, getBranchName, normalizeBranchName, getGitPath, getBranchs };