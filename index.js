#!/usr/bin/env node

const { Command } = require("commander");
const { addNetworkCommands } = require("./network/command.js");
const { addDeployCommands } = require("./deploy/command.js");
const { addTestCommands } = require("./test/command.js");
const { addReleaseCommand } = require("./release/command.js");
const { addDatabaseCommand } = require("./database/command.js");
const { addJiraCommands } = require("./jira/command.js");
const { addCodeCommand } = require("./code/command.js");
const { addIaCommands } = require("./ai/commands.js");
const { addLogsCommands } = require("./logs/command.js");

const program = new Command("pacto");

addNetworkCommands(program);
addDeployCommands(program);
addTestCommands(program);
addJiraCommands(program);
addReleaseCommand(program);
addDatabaseCommand(program);
addCodeCommand(program);
addIaCommands(program);
addLogsCommands(program);

program.parse(process.argv);
