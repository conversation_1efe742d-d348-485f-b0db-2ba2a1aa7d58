const inquirer = require('inquirer');
const  { getValue, setValue } = require('../util/storage');  

function getJiraCredentials() {
    let email = getValue('jira.email');
    let token = getValue('jira.token');
    if (email === undefined || token === undefined) {

        if(process.env.JIRA_EMAIL && process.env.JIRA_TOKEN){
            setValue('jira.email', process.env.JIRA_EMAIL);
            setValue('jira.token', process.env.JIRA_TOKEN);
            email = process.env.JIRA_EMAIL;
            token = process.env.JIRA_TOKEN;
        }else{
            console.error('Configuração do Jira não encontrada. Use "pacto jira credenciais" para configurar.');
            return;
        }
    }

    return { email, token };
}

async function configureJiraCredentials() {
   const emailConfig = getValue('jira.email');
   const {email} = await inquirer.prompt([
        {
            type: 'input',
            name: 'email',
            message: 'Informe seu e-mail do Jira:',
            default: emailConfig || '',
        },
    ]);
    const tokenConfig = getValue('jira.token');
    const {token} = await inquirer.prompt([
        {
            type: 'password',
            name: 'token',
            message: 'Informe seu token do Jira:',
            default: tokenConfig || '',
        },
    ]);

    setValue('jira.email', email);
    setValue('jira.token', token);
}

module.exports = { getJiraCredentials, configureJiraCredentials };