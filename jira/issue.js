const axios = require('axios');
const inquirer = require('inquirer');
const fs = require('fs').promises;
const open = require('open');
const autocompletePrompt = require('inquirer-autocomplete-prompt');
inquirer.registerPrompt('autocomplete', autocompletePrompt);

const credentialsFile = 'jiraCredentials.json';
const jiraUrl = 'https://pacto.atlassian.net';

// TODO: Este script é esperimental e está em desenvolvimento. Ele não está completo e não deve ser utilizado.

async function getUsername(apiToken, email) {
    const config = {
        headers: {
            'Authorization': `Basic ${Buffer.from(`${email}:${apiToken}`).toString('base64')}`,
            'Accept': 'application/json'
        }
    };

    try {
        const response = await axios.get(`${jiraUrl}/rest/api/3/myself`, config);
        return response.data.accountId;
    } catch (error) {
        console.error('Não foi possível obter o nome de usuário:', error);
    }
}

async function getCredentials() {
    let credentials;
    try {
        const data = await fs.readFile(credentialsFile, 'utf8');
        credentials = JSON.parse(data);
    } catch (err) {
        console.log('Credenciais do Jira não encontradas.');
        await open('https://id.atlassian.com/manage-profile/security/api-tokens');

        const answers = await inquirer.prompt([
            {
                type: 'string',
                name: 'email',
                message: 'Digite seu email utilizado no Jira:',
            },
            {
                type: 'string',
                name: 'apiToken',
                message: 'Digite seu token de API do Jira:',
            },
        ]);

        credentials = {
            email: answers.email,
            apiToken: answers.apiToken,
        };

        await fs.writeFile(credentialsFile, JSON.stringify(credentials), 'utf8');
    }

    return credentials;
}

async function fetchIssues(credentials) {
    const response = await axios.get(`${jiraUrl}/rest/api/2/search`, {
        auth: {
            username: credentials.email,
            password: credentials.apiToken,
        },
        params: {
            jql: 'order by updated DESC',
        },
    });

    return response.data.issues;
}

async function searchIssues(answers, input) {
    input = input || '';
    const credentials = await getCredentials();
    const issues = await fetchIssues(credentials);
    const filteredIssues = issues.filter(
        issue =>
            issue.key.toLowerCase().includes(input.toLowerCase()) ||
            issue.fields.summary.toLowerCase().includes(input.toLowerCase())
    );

    return filteredIssues.map(issue => ({
        name: `${issue.key} - ${issue.fields.summary}`,
        value: issue.key,
    }));
}

async function askIssue() {
    const answers = await inquirer.prompt([
        {
            type: 'autocomplete',
            name: 'issue',
            message: 'Selecione uma issue',
            source: searchIssues,
        },
    ]);

    console.log('Você selecionou:', answers.issue);
}

module.exports = { getUsername, getCredentials };