const { Command } = require('commander');
const main = require('./issue.js');
const jira = new Command('jira');

jira
  .command('issue')
  .description('Manipula issues do Jira')
  .action(() => {
    console.log('Manipulando issues do Jira...');
    try {
        main();
    } catch (error) {
        console.error('Ocorreu um erro ao manipular issues do Jira:', error);
    }
  });

module.exports = jira;