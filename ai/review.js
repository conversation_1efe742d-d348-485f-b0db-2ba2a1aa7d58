function findIssuesByProject(tags) {
    tags = Array.isArray(tags) ? tags : [tags];
    return [
        {
            tags: ['zw', 11991911],
            issues: "- Em Servlets não use Conexao.guardarConexaoForJ2SE() porque isso pode causar problemas com conexões de bancos de dados. " +
                "- Para classes de migração de banco de dados do projeto ZW (são as classes que estendem MigracaoVersaoInterface) você deve chegar se o atributo descrição está claro, se o SQL está correto e se o SQL não tem Drops, ou alterações que possar gerar problema de retrocompatibilidade." +
                "- Em Servlets feche as conexões de banco que foram abertas." +
                "- Em Servlets não use new DAO() que faz leitura de XML. Use Conexao.getInstance().getConnection()"
        },
        {
            tags: ['ms', 39423146, 36632339, 41130932],
            issues: "Os atributos mock: e ia: arquivo de application-dev.yml não devem ser alterado, pois ele tem as configurações de desenvolvimento para funcionar com a chave teste."
        }
    ].filter((item) => {
        return item.tags.some((tag) => tags.includes(tag));
    });
}

module.exports = { findIssuesByProject };