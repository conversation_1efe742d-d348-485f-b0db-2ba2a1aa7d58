const inquirer = require('inquirer');
const chalk = require('chalk');
const { setValue, getValue } = require('../util/storage');

function addStatusCommand(program) {
  
  program.command('status')
    .alias('st')
    .description('Exibe o modelo de IA configurado atualmente')
    .action(() => {
      try {
        const currentModel = getValue('ai_model');

        if (!currentModel) {
          console.log(chalk.yellow('Nenhum modelo de IA configurado. Use o comando `pacto ia config` para configurar.'));
          return;
        }

        console.log(chalk.green(`Modelo de IA atual: ${getModelDisplayName(currentModel)}`));

        switch (currentModel) {
          case 'openai':
            const openaiModel = getValue('openai_model');
            if (openaiModel) {
              console.log(chalk.green(`Modelo específico: ${openaiModel}`));
            }
            break;

          case 'gemini':
            const geminiModel = getValue('gemini_model');
            if (geminiModel) {
              console.log(chalk.green(`Modelo específico: ${geminiModel}`));
            }
            break;

          // Exibir configurações para outros modelos
        }

        // Verificar se o token está configurado
        const hasToken = !!getValue(`${currentModel}_token`);
        if (hasToken) {
          console.log(chalk.green('✓ Token configurado'));
        } else {
          console.log(chalk.yellow('⚠ Token não configurado'));
        }
      } catch (error) {
        console.error(chalk.red(`Erro ao verificar status: ${error.message}`));
      }
    });
}

function addConfigCommand(program) {
  
  program.command('config')
    .alias('c')
    .description('Configura qual modelo de IA será utilizado no comando ao executar operações do comando pacto.')
    .action(async () => {
      try {
        const { model } = await inquirer.prompt([
          {
            type: 'list',
            name: 'model',
            message: 'Escolha o modelo de IA que deseja utilizar:',
            choices: [
              { name: 'OpenAI (GPT-4, GPT-3.5)', value: 'openai' },
              { name: 'Google Gemini', value: 'gemini' },
              { name: 'Anthropic Claude', value: 'anthropic' },
              { name: 'Deepseek', value: 'deepseek' },
              { name: 'xAI Grok', value: 'grok' }
            ]
          }
        ]);

        // Configurar o token para o modelo selecionado
        const { token } = await inquirer.prompt([
          {
            type: 'password',
            name: 'token',
            message: `Digite o token de acesso para ${getModelDisplayName(model)}:`,
            validate: (input) => input.trim() !== '' ? true : 'O token não pode estar vazio'
          }
        ]);

        // Salvar o modelo selecionado e o token
        setValue('ai_model', model);
        setValue(`${model}_token`, token);

        // Configurações específicas para cada modelo
        switch (model) {
          case 'openai':
            const { openaiModel } = await inquirer.prompt([
              {
                type: 'list',
                name: 'openaiModel',
                message: 'Escolha o modelo específico da OpenAI:',
                choices: [
                  { name: 'GPT-4 Turbo', value: 'gpt-4-turbo-preview' },
                  { name: 'GPT-4', value: 'gpt-4' },
                  { name: 'GPT-3.5 Turbo', value: 'gpt-3.5-turbo' }
                ]
              }
            ]);
            setValue('openai_model', openaiModel);
            break;

          case 'gemini':
            const { geminiModel } = await inquirer.prompt([
              {
                type: 'list',
                name: 'geminiModel',
                message: 'Escolha o modelo específico do Gemini:',
                choices: [
                  { name: 'Gemini Pro', value: 'gemini-pro' },
                  { name: 'Gemini Ultra', value: 'gemini-ultra' }
                ]
              }
            ]);
            setValue('gemini_model', geminiModel);
            break;

          // Configurações adicionais para outros modelos podem ser adicionadas aqui
        }

        console.log(chalk.green(`✓ Configuração do modelo ${getModelDisplayName(model)} salva com sucesso!`));
      } catch (error) {
        console.error(chalk.red(`Erro ao configurar modelo de IA: ${error.message}`));
      }
    });
}

function getModelDisplayName(modelKey) {
  const modelNames = {
    'openai': 'OpenAI',
    'gemini': 'Google Gemini',
    'anthropic': 'Anthropic Claude',
    'deepseek': 'Deepseek',
    'grok': 'xAI Grok'
  };

  return modelNames[modelKey] || modelKey;
}

async function addIaCommands(program) {

  const iaCommand= program
        .command('ia')
        .description('Configura e gerencia integrações com modelos de IA');

  addConfigCommand(iaCommand);
  addStatusCommand(iaCommand);
}


module.exports = { addIaCommands, getModelDisplayName };
